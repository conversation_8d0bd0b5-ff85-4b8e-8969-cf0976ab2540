<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SOIC 社交创新社区</title>
    <meta name="description" content="SOIC - 沉浸式3D虚拟社交平台，连接创新者，共建未来社区" />
    <meta name="keywords" content="社交,创新,社区,3D,虚拟现实,元宇宙" />
    <meta name="author" content="SOIC Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://soic.com/" />
    <meta property="og:title" content="SOIC 社交创新社区" />
    <meta property="og:description" content="沉浸式3D虚拟社交平台，连接创新者，共建未来社区" />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://soic.com/" />
    <meta property="twitter:title" content="SOIC 社交创新社区" />
    <meta property="twitter:description" content="沉浸式3D虚拟社交平台，连接创新者，共建未来社区" />
    <meta property="twitter:image" content="/og-image.jpg" />
    
    <!-- PWA相关 -->
    <meta name="theme-color" content="#ffffff" />
    <link rel="manifest" href="/manifest.json" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 样式预加载 -->
    <style>
      /* 加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      .loading-text {
        color: white;
        font-size: 18px;
        margin-top: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* 基础样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f5f5f5;
      }
    </style>
  </head>
  <body>
    <!-- 加载动画 -->
    <div id="loading">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载 SOIC 社交创新社区...</div>
      </div>
    </div>
    
    <!-- Vue应用挂载点 -->
    <div id="app"></div>
    
    <!-- 主脚本 -->
    <script type="module" src="/src/main.ts"></script>
    
    <!-- 加载完成后隐藏加载动画 -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            setTimeout(function() {
              loading.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('页面加载错误:', e.error);
      });
      
      // 未处理的Promise错误
      window.addEventListener('unhandledrejection', function(e) {
        console.error('未处理的Promise错误:', e.reason);
      });
    </script>
  </body>
</html>
