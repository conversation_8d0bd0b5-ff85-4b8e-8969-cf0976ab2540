<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 全局加载进度条 -->
    <div v-if="loading" class="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-80">
      <div class="text-center">
        <el-icon class="animate-spin text-4xl text-blue-500 mb-4">
          <Loading />
        </el-icon>
        <p class="text-lg text-gray-600">正在加载 SOIC 社交创新社区...</p>
      </div>
    </div>

    <!-- 路由视图 -->
    <router-view v-slot="{ Component }" v-if="!loading">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { Loading } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(true)

onMounted(async () => {
  try {
    // 初始化应用
    console.log('🚀 SOIC前端应用启动中...')

    // 检查用户登录状态 (暂时注释掉，避免错误)
    // await userStore.checkAuthStatus()

    // 模拟加载时间
    setTimeout(() => {
      loading.value = false
      console.log('✅ SOIC前端应用启动完成！')
    }, 1500)

  } catch (error) {
    console.error('❌ 应用启动失败:', error)
    loading.value = false
  }
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
