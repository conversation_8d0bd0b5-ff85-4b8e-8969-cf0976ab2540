import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import router from './router'
import App from './App.vue'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 基础样式导入
// import '@/styles/global.scss'  // 暂时注释掉，避免SCSS错误

// WebSocket服务 - 暂时注释掉
// import { setupWebSocket } from '@/services/websocket'

// 工具函数
import { setupGlobalProperties } from '@/utils/global'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例并配置持久化
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 注册Element Plus
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
})

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册插件
app.use(pinia)
app.use(router)

// 设置WebSocket服务 - 暂时注释掉
// setupWebSocket(app)

// 设置全局属性
setupGlobalProperties(app)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  // 可以在这里集成错误监控服务
}

// 挂载应用
app.mount('#app')