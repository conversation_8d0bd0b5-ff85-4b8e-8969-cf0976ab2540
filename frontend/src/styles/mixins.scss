// SOIC 设计系统 Mixins

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
  @if $breakpoint == 2xl {
    @media (min-width: $breakpoint-2xl) {
      @content;
    }
  }
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Flexbox 布局
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  @include flex-column;
  align-items: center;
  justify-content: center;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 隐藏滚动条
@mixin hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 自定义滚动条
@mixin custom-scrollbar($width: 8px, $track-color: $gray-100, $thumb-color: $gray-400) {
  &::-webkit-scrollbar {
    width: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $width / 2;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}

// 按钮样式
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $button-padding-y $button-padding-x;
  border: 1px solid transparent;
  border-radius: $button-border-radius;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-base;
  user-select: none;
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.25);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-variant($bg-color, $text-color: $white, $border-color: $bg-color) {
  background-color: $bg-color;
  color: $text-color;
  border-color: $border-color;
  
  &:hover:not(:disabled) {
    background-color: darken($bg-color, 8%);
    border-color: darken($border-color, 8%);
  }
  
  &:active:not(:disabled) {
    background-color: darken($bg-color, 12%);
    border-color: darken($border-color, 12%);
  }
}

// 输入框样式
@mixin input-base {
  display: block;
  width: 100%;
  padding: $input-padding-y $input-padding-x;
  font-size: $font-size-base;
  line-height: 1.5;
  color: $text-primary;
  background-color: $bg-primary;
  border: 1px solid $border-color;
  border-radius: $input-border-radius;
  transition: border-color $transition-base, box-shadow $transition-base;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.25);
  }
  
  &::placeholder {
    color: $text-muted;
  }
  
  &:disabled {
    background-color: $gray-100;
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 卡片样式
@mixin card-base {
  background-color: $bg-primary;
  border: 1px solid $border-light;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
}

// 动画
@mixin fade-in($duration: $transition-base) {
  animation: fadeIn $duration ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@mixin slide-in-up($duration: $transition-base) {
  animation: slideInUp $duration ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@mixin slide-in-down($duration: $transition-base) {
  animation: slideInDown $duration ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 加载动画
@mixin loading-spinner($size: 20px, $color: $primary-color) {
  width: $size;
  height: $size;
  border: 2px solid rgba($color, 0.3);
  border-top-color: $color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 悬浮效果
@mixin hover-lift {
  transition: transform $transition-base, box-shadow $transition-base;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

// 渐变背景
@mixin gradient-bg($start-color, $end-color, $direction: 135deg) {
  background: linear-gradient($direction, $start-color 0%, $end-color 100%);
}

// 毛玻璃效果
@mixin glass-effect($opacity: 0.8) {
  background: rgba($white, $opacity);
  backdrop-filter: blur(10px);
  border: 1px solid rgba($white, 0.2);
}

// 社交媒体按钮
@mixin social-button($platform) {
  @if $platform == wechat {
    background-color: $social-wechat;
  } @else if $platform == qq {
    background-color: $social-qq;
  } @else if $platform == weibo {
    background-color: $social-weibo;
  } @else if $platform == github {
    background-color: $social-github;
  } @else if $platform == twitter {
    background-color: $social-twitter;
  } @else if $platform == facebook {
    background-color: $social-facebook;
  }
  
  color: $white;
  border: none;
  
  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }
}
