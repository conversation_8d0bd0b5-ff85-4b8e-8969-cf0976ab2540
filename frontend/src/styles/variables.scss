// SOIC 设计系统变量定义

// 颜色系统
$primary-color: #3498db;
$primary-light: #5dade2;
$primary-dark: #2980b9;

$secondary-color: #2ecc71;
$secondary-light: #58d68d;
$secondary-dark: #27ae60;

$accent-color: #e74c3c;
$accent-light: #ec7063;
$accent-dark: #c0392b;

$warning-color: #f39c12;
$warning-light: #f8c471;
$warning-dark: #e67e22;

$info-color: #3498db;
$success-color: #2ecc71;
$error-color: #e74c3c;

// 中性色
$white: #ffffff;
$gray-50: #f8f9fa;
$gray-100: #f1f3f4;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;
$black: #000000;

// 文本颜色
$text-primary: #2c3e50;
$text-secondary: #7f8c8d;
$text-muted: #95a5a6;
$text-disabled: #bdc3c7;

// 背景颜色
$bg-primary: #ffffff;
$bg-secondary: #f8f9fa;
$bg-tertiary: #ecf0f1;

// 边框颜色
$border-color: #dee2e6;
$border-light: #e9ecef;
$border-dark: #adb5bd;

// 阴影
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
$shadow-md: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
$shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
$shadow-xl: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);

// 字体
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

// 字体大小
$font-size-xs: 0.75rem;   // 12px
$font-size-sm: 0.875rem;  // 14px
$font-size-base: 1rem;    // 16px
$font-size-lg: 1.125rem;  // 18px
$font-size-xl: 1.25rem;   // 20px
$font-size-2xl: 1.5rem;   // 24px
$font-size-3xl: 1.875rem; // 30px
$font-size-4xl: 2.25rem;  // 36px

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// 间距
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px
$spacing-2xl: 3rem;     // 48px
$spacing-3xl: 4rem;     // 64px

// 边框半径
$border-radius-sm: 0.25rem;  // 4px
$border-radius-md: 0.375rem; // 6px
$border-radius-lg: 0.5rem;   // 8px
$border-radius-xl: 0.75rem;  // 12px
$border-radius-2xl: 1rem;    // 16px
$border-radius-full: 9999px;

// 断点
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Z-index
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;

// 过渡动画
$transition-fast: 0.15s ease-in-out;
$transition-base: 0.3s ease-in-out;
$transition-slow: 0.5s ease-in-out;

// 组件特定变量
$header-height: 64px;
$sidebar-width: 256px;
$sidebar-collapsed-width: 64px;

// 表单控件
$input-height: 40px;
$input-padding-x: 12px;
$input-padding-y: 8px;
$input-border-radius: $border-radius-md;

// 按钮
$button-height: 40px;
$button-padding-x: 16px;
$button-padding-y: 8px;
$button-border-radius: $border-radius-md;

// 卡片
$card-padding: 24px;
$card-border-radius: $border-radius-lg;
$card-shadow: $shadow-sm;

// 社交平台特定颜色
$social-wechat: #07c160;
$social-qq: #12b7f5;
$social-weibo: #e6162d;
$social-github: #333333;
$social-twitter: #1da1f2;
$social-facebook: #1877f2;
