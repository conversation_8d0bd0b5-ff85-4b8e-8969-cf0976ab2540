import type { App } from 'vue'

/**
 * 设置全局属性
 */
export function setupGlobalProperties(app: App) {
  // 全局属性配置
  app.config.globalProperties.$SOIC = {
    name: 'SOIC社交创新社区',
    version: '1.0.0',
    description: '沉浸式3D虚拟社交平台'
  }
  
  // 全局方法
  app.config.globalProperties.$formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('zh-CN')
  }
  
  app.config.globalProperties.$formatTime = (date: Date | string) => {
    return new Date(date).toLocaleString('zh-CN')
  }
}
