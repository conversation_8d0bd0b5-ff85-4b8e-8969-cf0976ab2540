<template>
  <div class="test-container">
    <h1>SOIC 前端测试页面</h1>
    <p>如果您看到这个页面，说明前端项目运行正常！</p>
    
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>系统状态</span>
        </div>
      </template>
      
      <div class="status-list">
        <div class="status-item">
          <el-icon><Check /></el-icon>
          <span>Vue 3 运行正常</span>
        </div>
        <div class="status-item">
          <el-icon><Check /></el-icon>
          <span>Element Plus 加载成功</span>
        </div>
        <div class="status-item">
          <el-icon><Check /></el-icon>
          <span>TypeScript 编译正常</span>
        </div>
      </div>
      
      <el-button type="primary" @click="testAPI">测试API连接</el-button>
      <el-button type="success" @click="showMessage">显示消息</el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Check } from '@element-plus/icons-vue'

const testAPI = () => {
  ElMessage.success('API测试功能待实现')
}

const showMessage = () => {
  ElMessage.info('前端系统运行正常！')
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-list {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #67c23a;
}

.status-item .el-icon {
  margin-right: 8px;
}
</style>
