import { io, type Socket } from 'socket.io-client'
import { ElMessage, ElNotification } from 'element-plus'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/stores/modules/user'
import { useMessagingStore } from '@/stores/modules/messaging'
import { useSocialStore } from '@/stores/modules/social'

/**
 * WebSocket服务类 - 实时通信管理
 * 职责：处理WebSocket连接、消息推送、事件分发
 */
class WebSocketService {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 5000
  private isConnecting = false

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve()
        return
      }

      if (this.isConnecting) {
        return
      }

      this.isConnecting = true
      const token = getToken()

      if (!token) {
        this.isConnecting = false
        reject(new Error('No authentication token'))
        return
      }

      // 创建Socket.IO连接
      this.socket = io(import.meta.env.VITE_WS_URL || 'ws://localhost:8000', {
        auth: {
          token: `Bearer ${token}`
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true
      })

      // 连接成功
      this.socket.on('connect', () => {
        console.log('WebSocket connected:', this.socket?.id)
        this.isConnecting = false
        this.reconnectAttempts = 0
        this.setupEventHandlers()
        resolve()
      })

      // 连接错误
      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error)
        this.isConnecting = false
        this.handleReconnect()
        reject(error)
      })

      // 连接断开
      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason)
        this.isConnecting = false
        
        if (reason === 'io server disconnect') {
          // 服务器主动断开，需要重新连接
          this.handleReconnect()
        }
      })

      // 认证错误
      this.socket.on('auth_error', (error) => {
        console.error('WebSocket auth error:', error)
        ElMessage.error('认证失败，请重新登录')
        // 清除token并跳转到登录页
        const userStore = useUserStore()
        userStore.logout()
      })
    })
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.reconnectAttempts = 0
    this.isConnecting = false
  }

  /**
   * 处理重连
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      ElMessage.error('连接失败，请刷新页面重试')
      return
    }

    this.reconnectAttempts++
    console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    setTimeout(() => {
      this.connect().catch((error) => {
        console.error('Reconnection failed:', error)
      })
    }, this.reconnectInterval * this.reconnectAttempts)
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.socket) return

    // ==================== 消息系统事件 ====================

    // 新消息
    this.socket.on('new_message', (data) => {
      console.log('New message received:', data)
      const messagingStore = useMessagingStore()
      messagingStore.handleNewMessage(data)
      
      // 显示通知
      if (data.sender.id !== useUserStore().currentUser?.id) {
        ElNotification({
          title: `来自 ${data.sender.displayName} 的消息`,
          message: data.content,
          type: 'info',
          duration: 3000,
          onClick: () => {
            // 跳转到对话页面
            window.$router?.push(`/messages/${data.conversationId}`)
          }
        })
      }
    })

    // 消息状态更新
    this.socket.on('message_status_update', (data) => {
      console.log('Message status updated:', data)
      const messagingStore = useMessagingStore()
      messagingStore.handleMessageStatusUpdate(data)
    })

    // 用户在线状态
    this.socket.on('user_online_status', (data) => {
      console.log('User online status:', data)
      const messagingStore = useMessagingStore()
      messagingStore.updateUserOnlineStatus(data.userId, data.isOnline, data.lastSeen)
    })

    // 正在输入状态
    this.socket.on('typing_status', (data) => {
      console.log('Typing status:', data)
      const messagingStore = useMessagingStore()
      messagingStore.updateTypingStatus(data.conversationId, data.userId, data.isTyping)
    })

    // ==================== 社交系统事件 ====================

    // 好友请求
    this.socket.on('friend_request', (data) => {
      console.log('Friend request received:', data)
      const socialStore = useSocialStore()
      socialStore.handleFriendRequest(data)
      
      ElNotification({
        title: '好友请求',
        message: `${data.sender.displayName} 想要添加您为好友`,
        type: 'info',
        duration: 5000,
        onClick: () => {
          window.$router?.push('/friends/requests')
        }
      })
    })

    // 好友请求状态更新
    this.socket.on('friend_request_update', (data) => {
      console.log('Friend request updated:', data)
      const socialStore = useSocialStore()
      socialStore.handleFriendRequestUpdate(data)
    })

    // 新关注者
    this.socket.on('new_follower', (data) => {
      console.log('New follower:', data)
      const socialStore = useSocialStore()
      socialStore.handleNewFollower(data)
      
      ElNotification({
        title: '新关注者',
        message: `${data.follower.displayName} 关注了您`,
        type: 'success',
        duration: 3000
      })
    })

    // 群组邀请
    this.socket.on('group_invitation', (data) => {
      console.log('Group invitation:', data)
      const socialStore = useSocialStore()
      socialStore.handleGroupInvitation(data)
      
      ElNotification({
        title: '群组邀请',
        message: `${data.inviter.displayName} 邀请您加入群组 "${data.group.name}"`,
        type: 'info',
        duration: 5000,
        onClick: () => {
          window.$router?.push(`/groups/${data.group.id}`)
        }
      })
    })

    // ==================== 系统通知事件 ====================

    // 系统通知
    this.socket.on('system_notification', (data) => {
      console.log('System notification:', data)
      
      ElNotification({
        title: data.title || '系统通知',
        message: data.message,
        type: data.type || 'info',
        duration: data.duration || 5000
      })
    })

    // 内容审核结果
    this.socket.on('content_moderation_result', (data) => {
      console.log('Content moderation result:', data)
      
      const typeMap = {
        approved: 'success',
        rejected: 'warning',
        deleted: 'error'
      }
      
      ElNotification({
        title: '内容审核结果',
        message: data.message,
        type: typeMap[data.status as keyof typeof typeMap] || 'info',
        duration: 5000
      })
    })

    // 经济系统事件
    this.socket.on('transaction_update', (data) => {
      console.log('Transaction update:', data)
      
      if (data.status === 'completed') {
        ElNotification({
          title: '交易完成',
          message: `您的${data.type}交易已完成`,
          type: 'success',
          duration: 3000
        })
      } else if (data.status === 'failed') {
        ElNotification({
          title: '交易失败',
          message: `您的${data.type}交易失败：${data.reason}`,
          type: 'error',
          duration: 5000
        })
      }
    })

    // ==================== 错误处理 ====================

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error)
      ElMessage.error(`连接错误: ${error.message}`)
    })
  }

  /**
   * 发送消息
   */
  emit(event: string, data?: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data)
    } else {
      console.warn('WebSocket not connected, cannot emit event:', event)
    }
  }

  /**
   * 加入房间
   */
  joinRoom(roomId: string): void {
    this.emit('join_room', { roomId })
  }

  /**
   * 离开房间
   */
  leaveRoom(roomId: string): void {
    this.emit('leave_room', { roomId })
  }

  /**
   * 发送正在输入状态
   */
  sendTypingStatus(conversationId: string, isTyping: boolean): void {
    this.emit('typing', { conversationId, isTyping })
  }

  /**
   * 标记消息为已读
   */
  markMessageAsRead(conversationId: string, messageId: string): void {
    this.emit('mark_as_read', { conversationId, messageId })
  }

  /**
   * 更新在线状态
   */
  updateOnlineStatus(isOnline: boolean): void {
    this.emit('update_online_status', { isOnline })
  }

  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.socket?.connected || false
  }

  /**
   * 获取连接ID
   */
  get connectionId(): string | undefined {
    return this.socket?.id
  }
}

// 创建全局WebSocket服务实例
export const wsService = new WebSocketService()

// 在Vue应用中注册WebSocket服务
export function setupWebSocket(app: any) {
  app.config.globalProperties.$ws = wsService
  window.$ws = wsService
}

// 类型声明
declare global {
  interface Window {
    $ws: WebSocketService
    $router: any
  }
}

export default WebSocketService
