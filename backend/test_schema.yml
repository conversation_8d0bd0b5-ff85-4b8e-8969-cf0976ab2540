openapi: 3.0.3
info:
  title: SOIC API - 社交创新社区
  version: 1.0.0
  description: "\n    ## SOIC 社交创新社区 API 文档\n\n    这是一个功能完整的社交平台后端API，提供以下核心功能：\n\n\
    \    ### \U0001F510 用户认证系统\n    - 用户注册、登录、密码重置\n    - JWT Token认证\n    - 用户资料管理\n\
    \    - 头像系统\n\n    ### \U0001F465 社交功能\n    - 好友关系管理\n    - 关注/粉丝系统\n    - 群组创建与管理\n\
    \    - 社交活动推荐\n\n    ### \U0001F4AC 消息系统\n    - 私聊功能\n    - 群聊功能\n    - 实时消息推送\n\
    \    - 消息历史记录\n\n    ### \U0001F4DD 内容管理\n    - 帖子发布与管理\n    - 评论系统\n    - 内容分类\n\
    \    - 内容审核\n\n    ### \U0001F4B0 经济系统\n    - 虚拟货币管理\n    - 钱包功能\n    - 交易系统\n\
    \    - 商品管理\n\n    ### 认证方式\n    在请求头中添加：`Authorization: Bearer <your_jwt_token>`\n\
    \n    ### 时间格式\n    所有时间字段均使用 ISO 8601 格式，时区为 Asia/Shanghai\n    "
  contact:
    name: SOIC 开发团队
    email: <EMAIL>
  license:
    name: MIT License
paths:
  /:
    get:
      operationId: root_retrieve
      description: 获取API信息
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/auth/avatars/:
    get:
      operationId: auth_avatars_list
      description: 用户虚拟形象视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserAvatarList'
          description: ''
    post:
      operationId: auth_avatars_create
      description: 用户虚拟形象视图集
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserAvatarRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserAvatarRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserAvatarRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAvatar'
          description: ''
  /api/v1/auth/avatars/{id}/:
    get:
      operationId: auth_avatars_retrieve
      description: 用户虚拟形象视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAvatar'
          description: ''
    put:
      operationId: auth_avatars_update
      description: 更新虚拟形象
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserAvatarRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserAvatarRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserAvatarRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAvatar'
          description: ''
    patch:
      operationId: auth_avatars_partial_update
      description: 部分更新虚拟形象
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserAvatarRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserAvatarRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserAvatarRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAvatar'
          description: ''
    delete:
      operationId: auth_avatars_destroy
      description: 用户虚拟形象视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/auth/profiles/:
    get:
      operationId: auth_profiles_list
      description: 用户资料视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserProfileList'
          description: ''
    post:
      operationId: auth_profiles_create
      description: 用户资料视图集
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserProfileRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserProfileRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserProfileRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
  /api/v1/auth/profiles/{id}/:
    get:
      operationId: auth_profiles_retrieve
      description: 用户资料视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
    put:
      operationId: auth_profiles_update
      description: 更新用户资料
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserProfileRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserProfileRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserProfileRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
    patch:
      operationId: auth_profiles_partial_update
      description: 部分更新用户资料
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserProfileRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserProfileRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserProfileRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: ''
    delete:
      operationId: auth_profiles_destroy
      description: 用户资料视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/auth/sessions/:
    get:
      operationId: auth_sessions_list
      description: 获取会话列表
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserSessionList'
          description: ''
  /api/v1/auth/sessions/{id}/:
    get:
      operationId: auth_sessions_retrieve
      description: 用户会话视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSession'
          description: ''
  /api/v1/auth/sessions/{id}/revoke/:
    delete:
      operationId: auth_sessions_revoke_destroy
      description: 撤销会话
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/auth/users/:
    get:
      operationId: auth_users_list
      description: 获取系统中的用户列表，支持分页和筛选
      summary: 获取用户列表
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - 用户认证
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: auth_users_create
      description: 创建新用户账户（管理员功能）
      summary: 创建用户
      tags:
      - 用户认证
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/{id}/:
    get:
      operationId: auth_users_retrieve
      description: 根据用户ID获取用户的详细信息
      summary: 获取用户详情
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - 用户认证
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: auth_users_update
      description: 更新用户的基本信息
      summary: 更新用户信息
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - 用户认证
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    patch:
      operationId: auth_users_partial_update
      description: 部分更新用户的基本信息
      summary: 部分更新用户信息
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - 用户认证
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    delete:
      operationId: auth_users_destroy
      description: 删除用户账户（管理员功能）
      summary: 删除用户
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - 用户认证
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/auth/users/{id}/profile/:
    get:
      operationId: auth_users_profile_retrieve
      description: |-
        获取用户公开资料

        GET /api/v1/users/{id}/profile/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/change_password/:
    post:
      operationId: auth_users_change_password_create
      description: |-
        修改密码

        POST /api/v1/users/change_password/
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/login/:
    post:
      operationId: auth_users_login_create
      description: 用户登录验证，支持用户名或邮箱登录，返回JWT访问令牌
      summary: 用户登录
      tags:
      - 用户认证
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLoginRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserLoginRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserLoginRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
  /api/v1/auth/users/logout/:
    post:
      operationId: auth_users_logout_create
      description: |-
        用户登出接口

        POST /api/v1/users/logout/
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/me/:
    get:
      operationId: auth_users_me_retrieve
      description: |-
        获取当前用户信息

        GET /api/v1/users/me/
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/register/:
    post:
      operationId: auth_users_register_create
      description: 注册新用户账户，需要提供用户名、邮箱、密码等信息
      summary: 用户注册
      tags:
      - 用户认证
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
  /api/v1/auth/users/revoke_session/:
    delete:
      operationId: auth_users_revoke_session_destroy
      description: |-
        撤销用户会话

        DELETE /api/v1/users/revoke_session/
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/auth/users/search/:
    get:
      operationId: auth_users_search_retrieve
      description: |-
        搜索用户

        GET /api/v1/users/search/?q=keyword&limit=20
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/send_verification_email/:
    post:
      operationId: auth_users_send_verification_email_create
      description: |-
        发送邮箱验证邮件

        POST /api/v1/users/send_verification_email/
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/sessions/:
    get:
      operationId: auth_users_sessions_retrieve
      description: |-
        获取用户会话列表

        GET /api/v1/users/sessions/
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/stats/:
    get:
      operationId: auth_users_stats_retrieve
      description: |-
        获取用户统计信息

        GET /api/v1/users/stats/
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/update_avatar/:
    patch:
      operationId: auth_users_update_avatar_partial_update
      description: |-
        更新用户虚拟形象

        PATCH /api/v1/users/update_avatar/
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/update_profile/:
    patch:
      operationId: auth_users_update_profile_partial_update
      description: |-
        更新用户资料

        PATCH /api/v1/users/update_profile/
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/auth/users/verify_email/:
    post:
      operationId: auth_users_verify_email_create
      description: |-
        验证邮箱

        POST /api/v1/users/verify_email/
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/content/comments/:
    post:
      operationId: content_comments_create
      description: |-
        创建评论

        POST /api/v1/comments/
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          description: No response body
  /api/v1/content/comments/by_post/:
    get:
      operationId: content_comments_by_post_retrieve
      description: |-
        获取帖子的评论列表

        GET /api/v1/comments/by_post/?post_id=xxx&page=1&page_size=20
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/content/content-categories/:
    get:
      operationId: content_content_categories_retrieve
      description: 内容分类视图集
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/content/content-categories/{id}/:
    get:
      operationId: content_content_categories_retrieve_2
      description: 内容分类视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/content/content-categories/list_categories/:
    get:
      operationId: content_content_categories_list_categories_retrieve
      description: |-
        获取内容分类列表

        GET /api/v1/content-categories/list_categories/
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/content/content-stats/platform_stats/:
    get:
      operationId: content_content_stats_platform_stats_retrieve
      description: |-
        获取平台内容统计（需要管理员权限）

        GET /api/v1/content-stats/platform_stats/
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/content/content-stats/user_stats/:
    get:
      operationId: content_content_stats_user_stats_retrieve
      description: |-
        获取用户内容统计

        GET /api/v1/content-stats/user_stats/
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/content/posts/:
    get:
      operationId: content_posts_retrieve
      description: |-
        获取帖子列表

        GET /api/v1/posts/?category_id=xxx&post_type=text&page=1&page_size=20
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: content_posts_create
      description: |-
        创建帖子

        POST /api/v1/posts/
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          description: No response body
  /api/v1/content/posts/{id}/:
    get:
      operationId: content_posts_retrieve_2
      description: |-
        获取帖子详情

        GET /api/v1/posts/{id}/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/content/posts/{id}/like/:
    post:
      operationId: content_posts_like_create
      description: |-
        点赞帖子

        POST /api/v1/posts/{id}/like/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/content/posts/{id}/moderate/:
    post:
      operationId: content_posts_moderate_create
      description: |-
        审核帖子

        POST /api/v1/posts/{id}/moderate/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/content/posts/{id}/unlike/:
    post:
      operationId: content_posts_unlike_create
      description: |-
        取消点赞帖子

        POST /api/v1/posts/{id}/unlike/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/content/posts/recommended/:
    get:
      operationId: content_posts_recommended_retrieve
      description: |-
        获取推荐帖子

        GET /api/v1/posts/recommended/?limit=10
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/content/posts/search/:
    get:
      operationId: content_posts_search_retrieve
      description: |-
        搜索帖子

        GET /api/v1/posts/search/?q=keyword&category_id=xxx&page=1&page_size=20
      tags:
      - content
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/core/health/:
    get:
      operationId: core_health_retrieve
      description: 获取系统健康状态
      tags:
      - core
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/core/info/:
    get:
      operationId: core_info_retrieve
      description: 获取API信息
      tags:
      - core
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/core/stats/:
    get:
      operationId: core_stats_retrieve
      description: 获取系统统计信息
      tags:
      - core
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/economy/economy-stats/platform_stats/:
    get:
      operationId: economy_economy_stats_platform_stats_retrieve
      description: |-
        获取平台经济统计（需要管理员权限）

        GET /api/v1/economy-stats/platform_stats/
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/economy/economy-stats/user_stats/:
    get:
      operationId: economy_economy_stats_user_stats_retrieve
      description: |-
        获取用户经济统计

        GET /api/v1/economy-stats/user_stats/
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/economy/orders/:
    get:
      operationId: economy_orders_retrieve
      description: |-
        获取用户订单列表

        GET /api/v1/orders/?status=pending&page=1&page_size=20
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: economy_orders_create
      description: |-
        创建订单

        POST /api/v1/orders/
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          description: No response body
  /api/v1/economy/orders/{id}/pay/:
    post:
      operationId: economy_orders_pay_create
      description: |-
        支付订单

        POST /api/v1/orders/{id}/pay/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/economy/products/:
    get:
      operationId: economy_products_retrieve
      description: |-
        获取商品列表

        GET /api/v1/products/?category=avatar&product_type=virtual_item&page=1&page_size=20
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/economy/products/featured/:
    get:
      operationId: economy_products_featured_retrieve
      description: |-
        获取精选商品

        GET /api/v1/products/featured/?limit=10
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/economy/transactions/:
    get:
      operationId: economy_transactions_retrieve
      description: |-
        获取交易历史

        GET /api/v1/transactions/?type=transfer&page=1&page_size=20
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/economy/wallets/balance/:
    get:
      operationId: economy_wallets_balance_retrieve
      description: |-
        获取钱包余额

        GET /api/v1/wallets/balance/?currency=COIN
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/economy/wallets/transfer/:
    post:
      operationId: economy_wallets_transfer_create
      description: |-
        转账

        POST /api/v1/wallets/transfer/
      tags:
      - economy
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/messaging/conversations/:
    post:
      operationId: messaging_conversations_create
      description: 创建新的会话（私聊或群聊）
      summary: 创建会话
      tags:
      - 消息系统
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          description: No response body
  /api/v1/messaging/conversations/{id}/messages/:
    get:
      operationId: messaging_conversations_messages_retrieve
      description: |-
        获取会话消息列表

        GET /api/v1/conversations/{id}/messages/?page=1&page_size=50
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - messaging
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/messaging/conversations/{id}/statistics/:
    get:
      operationId: messaging_conversations_statistics_retrieve
      description: |-
        获取会话统计信息

        GET /api/v1/conversations/{id}/statistics/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - messaging
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/messaging/conversations/list_conversations/:
    get:
      operationId: messaging_conversations_list_conversations_retrieve
      description: |-
        获取用户会话列表

        GET /api/v1/conversations/list_conversations/
      tags:
      - messaging
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/messaging/messages/:
    post:
      operationId: messaging_messages_create
      description: 在指定会话中发送新消息
      summary: 发送消息
      tags:
      - 消息系统
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          description: No response body
  /api/v1/messaging/messages/{id}/add_reaction/:
    post:
      operationId: messaging_messages_add_reaction_create
      description: |-
        添加消息反应

        POST /api/v1/messages/{id}/add_reaction/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - messaging
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/messaging/messages/{id}/mark_read/:
    post:
      operationId: messaging_messages_mark_read_create
      description: |-
        标记消息为已读

        POST /api/v1/messages/{id}/mark_read/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - messaging
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/messaging/messages/search/:
    get:
      operationId: messaging_messages_search_retrieve
      description: |-
        搜索消息

        GET /api/v1/messages/search/?q=keyword&conversation_id=xxx&page=1&page_size=20
      tags:
      - messaging
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/messaging/messaging-stats/overview/:
    get:
      operationId: messaging_messaging_stats_overview_retrieve
      description: |-
        获取消息统计概览

        GET /api/v1/messaging-stats/overview/
      tags:
      - messaging
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/messaging/online-status/friends_status/:
    get:
      operationId: messaging_online_status_friends_status_retrieve
      description: |-
        获取好友在线状态

        GET /api/v1/online-status/friends_status/
      tags:
      - messaging
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/messaging/online-status/update_status/:
    post:
      operationId: messaging_online_status_update_status_create
      description: |-
        更新在线状态

        POST /api/v1/online-status/update_status/
      tags:
      - messaging
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/follows/follow/:
    post:
      operationId: social_follows_follow_create
      description: |-
        关注用户

        POST /api/v1/follows/follow/
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/follows/followers/:
    get:
      operationId: social_follows_followers_retrieve
      description: |-
        获取粉丝列表

        GET /api/v1/follows/followers/
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/follows/following/:
    get:
      operationId: social_follows_following_retrieve
      description: |-
        获取关注列表

        GET /api/v1/follows/following/
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/follows/unfollow/:
    post:
      operationId: social_follows_unfollow_create
      description: |-
        取消关注用户

        POST /api/v1/follows/unfollow/
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/friendships/{id}/respond/:
    post:
      operationId: social_friendships_respond_create
      description: |-
        响应好友请求

        POST /api/v1/friendships/{id}/respond/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/friendships/friends/:
    get:
      operationId: social_friendships_friends_retrieve
      description: |-
        获取好友列表

        GET /api/v1/friendships/friends/
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/friendships/requests/:
    get:
      operationId: social_friendships_requests_retrieve
      description: |-
        获取好友请求列表

        GET /api/v1/friendships/requests/?type=received
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/friendships/send_request/:
    post:
      operationId: social_friendships_send_request_create
      description: |-
        发送好友请求

        POST /api/v1/friendships/send_request/
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/groups/:
    get:
      operationId: social_groups_list
      description: 获取系统中的群组列表，支持搜索和筛选
      summary: 获取群组列表
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - 社交功能
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedGroupList'
          description: ''
    post:
      operationId: social_groups_create
      description: 创建新的群组
      summary: 创建群组
      tags:
      - 社交功能
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GroupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GroupRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
          description: ''
  /api/v1/social/groups/{id}/:
    get:
      operationId: social_groups_retrieve
      description: 获取指定群组的详细信息
      summary: 获取群组详情
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - 社交功能
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
          description: ''
    put:
      operationId: social_groups_update
      description: 更新群组的基本信息（仅群主和管理员）
      summary: 更新群组信息
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - 社交功能
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GroupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GroupRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
          description: ''
    patch:
      operationId: social_groups_partial_update
      description: 群组视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - social
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedGroupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedGroupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedGroupRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
          description: ''
    delete:
      operationId: social_groups_destroy
      description: 删除群组（仅群主）
      summary: 删除群组
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - 社交功能
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/social/groups/{id}/join/:
    post:
      operationId: social_groups_join_create
      description: |-
        加入群组

        POST /api/v1/groups/{id}/join/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - social
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GroupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GroupRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
          description: ''
  /api/v1/social/groups/{id}/leave/:
    post:
      operationId: social_groups_leave_create
      description: |-
        离开群组

        POST /api/v1/groups/{id}/leave/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - social
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GroupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GroupRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
          description: ''
  /api/v1/social/groups/{id}/members/:
    get:
      operationId: social_groups_members_retrieve
      description: |-
        获取群组成员列表

        GET /api/v1/groups/{id}/members/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
          description: ''
  /api/v1/social/groups/my_groups/:
    get:
      operationId: social_groups_my_groups_retrieve
      description: |-
        获取我的群组列表

        GET /api/v1/groups/my_groups/?role=owner
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
          description: ''
  /api/v1/social/recommendations/{id}/dismiss/:
    post:
      operationId: social_recommendations_dismiss_create
      description: |-
        忽略推荐

        POST /api/v1/recommendations/{id}/dismiss/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/recommendations/follows/:
    get:
      operationId: social_recommendations_follows_retrieve
      description: |-
        获取关注推荐

        GET /api/v1/recommendations/follows/?limit=10
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/recommendations/friends/:
    get:
      operationId: social_recommendations_friends_retrieve
      description: |-
        获取好友推荐

        GET /api/v1/recommendations/friends/?limit=10
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/recommendations/generate/:
    post:
      operationId: social_recommendations_generate_create
      description: |-
        生成推荐

        POST /api/v1/recommendations/generate/
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/social-activities/:
    get:
      operationId: social_social_activities_retrieve
      description: 获取社交活动动态，包括好友动态、群组活动等
      summary: 获取社交活动列表
      tags:
      - 社交功能
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/social-activities/{id}/:
    get:
      operationId: social_social_activities_retrieve_2
      description: 获取指定社交活动的详细信息
      summary: 获取社交活动详情
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - 社交功能
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/social-activities/my_activities/:
    get:
      operationId: social_social_activities_my_activities_retrieve
      description: |-
        获取我的社交活动

        GET /api/v1/social-activities/my_activities/?limit=20
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/social-activities/timeline/:
    get:
      operationId: social_social_activities_timeline_retrieve
      description: |-
        获取社交活动时间线

        GET /api/v1/social-activities/timeline/?limit=20
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/social/social-stats/{id}/user_stats/:
    get:
      operationId: social_social_stats_user_stats_retrieve
      description: |-
        获取用户社交统计（公开）

        GET /api/v1/social-stats/{user_id}/user_stats/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/social/social-stats/overview/:
    get:
      operationId: social_social_stats_overview_retrieve
      description: |-
        获取社交统计概览

        GET /api/v1/social-stats/overview/
      tags:
      - social
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
components:
  schemas:
    BlankEnum:
      enum:
      - ''
    GenderEnum:
      enum:
      - male
      - female
      - other
      - prefer_not_to_say
      type: string
      description: |-
        * `male` - 男
        * `female` - 女
        * `other` - 其他
        * `prefer_not_to_say` - 不愿透露
    Group:
      type: object
      description: 群组序列化器
      properties:
        id:
          type: string
          readOnly: true
        name:
          type: string
          title: 群组名称
          maxLength: 100
        description:
          type: string
          title: 群组描述
        owner:
          type: string
          readOnly: true
        group_type:
          allOf:
          - $ref: '#/components/schemas/GroupTypeEnum'
          title: 群组类型
        status:
          allOf:
          - $ref: '#/components/schemas/GroupStatusEnum'
          title: 状态
        max_members:
          type: integer
          maximum: 10000
          minimum: 2
          title: 最大成员数
        current_members:
          type: integer
          readOnly: true
          title: 当前成员数
        member_rate:
          type: string
          readOnly: true
        is_full:
          type: string
          readOnly: true
        allow_member_invite:
          type: boolean
          title: 允许成员邀请
        require_approval:
          type: boolean
          title: 需要审批
        allow_anonymous_posts:
          type: boolean
          title: 允许匿名发帖
        avatar_url:
          type: string
          format: uri
          title: 群组头像
          maxLength: 200
        cover_url:
          type: string
          format: uri
          title: 群组封面
          maxLength: 200
        post_count:
          type: integer
          readOnly: true
          title: 帖子数量
        activity_count:
          type: integer
          readOnly: true
          title: 活动数量
        tags:
          title: 标签
        user_membership:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - name
    GroupRequest:
      type: object
      description: 群组序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 群组名称
          maxLength: 100
        description:
          type: string
          title: 群组描述
        group_type:
          allOf:
          - $ref: '#/components/schemas/GroupTypeEnum'
          title: 群组类型
        status:
          allOf:
          - $ref: '#/components/schemas/GroupStatusEnum'
          title: 状态
        max_members:
          type: integer
          maximum: 10000
          minimum: 2
          title: 最大成员数
        allow_member_invite:
          type: boolean
          title: 允许成员邀请
        require_approval:
          type: boolean
          title: 需要审批
        allow_anonymous_posts:
          type: boolean
          title: 允许匿名发帖
        avatar_url:
          type: string
          format: uri
          title: 群组头像
          maxLength: 200
        cover_url:
          type: string
          format: uri
          title: 群组封面
          maxLength: 200
        tags:
          title: 标签
      required:
      - name
    GroupStatusEnum:
      enum:
      - active
      - inactive
      - pending
      - archived
      type: string
      description: |-
        * `active` - 活跃
        * `inactive` - 非活跃
        * `pending` - 待处理
        * `archived` - 已归档
    GroupTypeEnum:
      enum:
      - public
      - private
      - secret
      type: string
      description: |-
        * `public` - 公开群组
        * `private` - 私有群组
        * `secret` - 秘密群组
    PaginatedGroupList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Group'
    PaginatedUserAvatarList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UserAvatar'
    PaginatedUserList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/User'
    PaginatedUserProfileList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UserProfile'
    PaginatedUserSessionList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UserSession'
    PatchedGroupRequest:
      type: object
      description: 群组序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 群组名称
          maxLength: 100
        description:
          type: string
          title: 群组描述
        group_type:
          allOf:
          - $ref: '#/components/schemas/GroupTypeEnum'
          title: 群组类型
        status:
          allOf:
          - $ref: '#/components/schemas/GroupStatusEnum'
          title: 状态
        max_members:
          type: integer
          maximum: 10000
          minimum: 2
          title: 最大成员数
        allow_member_invite:
          type: boolean
          title: 允许成员邀请
        require_approval:
          type: boolean
          title: 需要审批
        allow_anonymous_posts:
          type: boolean
          title: 允许匿名发帖
        avatar_url:
          type: string
          format: uri
          title: 群组头像
          maxLength: 200
        cover_url:
          type: string
          format: uri
          title: 群组封面
          maxLength: 200
        tags:
          title: 标签
    PatchedUserAvatarRequest:
      type: object
      description: 用户虚拟形象序列化器（用于API）
      properties:
        name:
          type: string
          minLength: 1
          title: 形象名称
          description: 头像名称，最多50个字符
          maxLength: 50
        model_url:
          type: string
          format: uri
          minLength: 1
          title: 3D模型URL
          description: 3D模型文件URL
          maxLength: 200
        thumbnail_url:
          type: string
          format: uri
          title: 缩略图URL
          description: 头像缩略图URL
          maxLength: 200
        config_data:
          title: 配置数据
          description: 头像配置数据，包含性别、肤色、发色、眼色等信息
        is_default:
          type: boolean
          title: 是否默认
          description: 是否为默认头像
    PatchedUserProfileRequest:
      type: object
      description: 用户资料序列化器
      properties:
        nickname:
          type: string
          minLength: 1
          title: 昵称
          description: 用户昵称
          maxLength: 50
        bio:
          type: string
          title: 个人简介
          description: 个人简介，最多500字符
          maxLength: 500
        birth_date:
          type: string
          format: date
          nullable: true
          description: 生日
        gender:
          title: 性别
          description: |-
            性别：male(男), female(女), other(其他), prefer_not_to_say(不愿透露)

            * `male` - 男
            * `female` - 女
            * `other` - 其他
            * `prefer_not_to_say` - 不愿透露
          oneOf:
          - $ref: '#/components/schemas/GenderEnum'
          - $ref: '#/components/schemas/BlankEnum'
        avatar_url:
          type: string
          format: uri
          title: 头像URL
          description: 头像图片URL
          maxLength: 200
        location:
          type: string
          title: 位置
          description: 所在位置
          maxLength: 100
        website:
          type: string
          format: uri
          title: 个人网站
          description: 个人网站URL
          maxLength: 200
        social_links:
          title: 社交链接
          description: 社交媒体链接，JSON格式
        privacy_settings:
          title: 隐私设置
          description: 隐私设置，JSON格式
    PatchedUserRequest:
      type: object
      description: 用户序列化器
      properties:
        status:
          allOf:
          - $ref: '#/components/schemas/UserStatusEnum'
          title: 状态
          description: |-
            用户状态：active(活跃), inactive(非活跃), suspended(暂停), banned(封禁)

            * `active` - 活跃
            * `inactive` - 非活跃
            * `suspended` - 暂停
            * `banned` - 封禁
        user_type:
          allOf:
          - $ref: '#/components/schemas/UserTypeEnum'
          title: 用户类型
          description: |-
            用户类型：regular(普通), premium(高级), vip(VIP), admin(管理员)

            * `regular` - 普通用户
            * `premium` - 高级用户
            * `vip` - VIP用户
            * `admin` - 管理员
    User:
      type: object
      description: 用户序列化器
      properties:
        id:
          type: integer
          readOnly: true
          description: 用户唯一标识符
        username:
          type: string
          readOnly: true
          title: 用户名
          description: 用户名
        email:
          type: string
          format: email
          readOnly: true
          title: 邮箱
          description: 邮箱地址
        display_name:
          type: string
          readOnly: true
          description: 用户显示名称
        is_active:
          type: boolean
          readOnly: true
          title: 有效
          description: 账户是否激活
        is_verified:
          type: boolean
          readOnly: true
          title: 是否认证
          description: 是否已验证邮箱
        status:
          allOf:
          - $ref: '#/components/schemas/UserStatusEnum'
          title: 状态
          description: |-
            用户状态：active(活跃), inactive(非活跃), suspended(暂停), banned(封禁)

            * `active` - 活跃
            * `inactive` - 非活跃
            * `suspended` - 暂停
            * `banned` - 封禁
        user_type:
          allOf:
          - $ref: '#/components/schemas/UserTypeEnum'
          title: 用户类型
          description: |-
            用户类型：regular(普通), premium(高级), vip(VIP), admin(管理员)

            * `regular` - 普通用户
            * `premium` - 高级用户
            * `vip` - VIP用户
            * `admin` - 管理员
        login_count:
          type: integer
          readOnly: true
          title: 登录次数
          description: 登录次数
        last_login:
          type: string
          format: date-time
          readOnly: true
          description: 最后登录时间
        permissions:
          type: string
          readOnly: true
          description: 用户权限列表
        created_at:
          type: string
          format: date-time
          readOnly: true
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          description: 更新时间
    UserAvatar:
      type: object
      description: 用户虚拟形象序列化器（用于API）
      properties:
        id:
          type: integer
          readOnly: true
          description: 头像唯一标识符
        user_id:
          type: string
          readOnly: true
          description: 关联用户ID
        name:
          type: string
          title: 形象名称
          description: 头像名称，最多50个字符
          maxLength: 50
        model_url:
          type: string
          format: uri
          title: 3D模型URL
          description: 3D模型文件URL
          maxLength: 200
        thumbnail_url:
          type: string
          format: uri
          title: 缩略图URL
          description: 头像缩略图URL
          maxLength: 200
        config_data:
          title: 配置数据
          description: 头像配置数据，包含性别、肤色、发色、眼色等信息
        is_default:
          type: boolean
          title: 是否默认
          description: 是否为默认头像
        created_at:
          type: string
          format: date-time
          readOnly: true
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          description: 更新时间
      required:
      - model_url
      - name
    UserAvatarRequest:
      type: object
      description: 用户虚拟形象序列化器（用于API）
      properties:
        name:
          type: string
          minLength: 1
          title: 形象名称
          description: 头像名称，最多50个字符
          maxLength: 50
        model_url:
          type: string
          format: uri
          minLength: 1
          title: 3D模型URL
          description: 3D模型文件URL
          maxLength: 200
        thumbnail_url:
          type: string
          format: uri
          title: 缩略图URL
          description: 头像缩略图URL
          maxLength: 200
        config_data:
          title: 配置数据
          description: 头像配置数据，包含性别、肤色、发色、眼色等信息
        is_default:
          type: boolean
          title: 是否默认
          description: 是否为默认头像
      required:
      - model_url
      - name
    UserLoginRequest:
      type: object
      description: 用户登录序列化器
      properties:
        identifier:
          type: string
          minLength: 1
          description: 用户名或邮箱
        password:
          type: string
          writeOnly: true
          minLength: 1
          description: 密码
        remember_me:
          type: boolean
          default: false
          description: 记住我
      required:
      - identifier
      - password
    UserProfile:
      type: object
      description: 用户资料序列化器
      properties:
        id:
          type: integer
          readOnly: true
          description: 资料唯一标识符
        user_id:
          type: string
          readOnly: true
          description: 关联用户ID
        nickname:
          type: string
          title: 昵称
          description: 用户昵称
          maxLength: 50
        bio:
          type: string
          title: 个人简介
          description: 个人简介，最多500字符
          maxLength: 500
        birth_date:
          type: string
          format: date
          nullable: true
          description: 生日
        gender:
          title: 性别
          description: |-
            性别：male(男), female(女), other(其他), prefer_not_to_say(不愿透露)

            * `male` - 男
            * `female` - 女
            * `other` - 其他
            * `prefer_not_to_say` - 不愿透露
          oneOf:
          - $ref: '#/components/schemas/GenderEnum'
          - $ref: '#/components/schemas/BlankEnum'
        avatar_url:
          type: string
          format: uri
          title: 头像URL
          description: 头像图片URL
          maxLength: 200
        location:
          type: string
          title: 位置
          description: 所在位置
          maxLength: 100
        website:
          type: string
          format: uri
          title: 个人网站
          description: 个人网站URL
          maxLength: 200
        social_links:
          title: 社交链接
          description: 社交媒体链接，JSON格式
        privacy_settings:
          title: 隐私设置
          description: 隐私设置，JSON格式
        created_at:
          type: string
          format: date-time
          readOnly: true
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          description: 更新时间
      required:
      - nickname
    UserProfileRequest:
      type: object
      description: 用户资料序列化器
      properties:
        nickname:
          type: string
          minLength: 1
          title: 昵称
          description: 用户昵称
          maxLength: 50
        bio:
          type: string
          title: 个人简介
          description: 个人简介，最多500字符
          maxLength: 500
        birth_date:
          type: string
          format: date
          nullable: true
          description: 生日
        gender:
          title: 性别
          description: |-
            性别：male(男), female(女), other(其他), prefer_not_to_say(不愿透露)

            * `male` - 男
            * `female` - 女
            * `other` - 其他
            * `prefer_not_to_say` - 不愿透露
          oneOf:
          - $ref: '#/components/schemas/GenderEnum'
          - $ref: '#/components/schemas/BlankEnum'
        avatar_url:
          type: string
          format: uri
          title: 头像URL
          description: 头像图片URL
          maxLength: 200
        location:
          type: string
          title: 位置
          description: 所在位置
          maxLength: 100
        website:
          type: string
          format: uri
          title: 个人网站
          description: 个人网站URL
          maxLength: 200
        social_links:
          title: 社交链接
          description: 社交媒体链接，JSON格式
        privacy_settings:
          title: 隐私设置
          description: 隐私设置，JSON格式
      required:
      - nickname
    UserRegistrationRequest:
      type: object
      description: 用户注册序列化器
      properties:
        username:
          type: string
          minLength: 3
          description: 用户名，3-150个字符
          maxLength: 150
        email:
          type: string
          format: email
          minLength: 1
          description: 邮箱地址
        password:
          type: string
          writeOnly: true
          minLength: 8
          description: 密码，至少8个字符
        confirm_password:
          type: string
          writeOnly: true
          minLength: 1
          description: 确认密码
        nickname:
          type: string
          minLength: 1
          description: 昵称，可选
          maxLength: 50
        agree_to_terms:
          type: boolean
          writeOnly: true
          description: 是否同意服务条款
        subscribe_newsletter:
          type: boolean
          default: false
          description: 是否订阅新闻通讯
      required:
      - agree_to_terms
      - confirm_password
      - email
      - password
      - username
    UserRequest:
      type: object
      description: 用户序列化器
      properties:
        status:
          allOf:
          - $ref: '#/components/schemas/UserStatusEnum'
          title: 状态
          description: |-
            用户状态：active(活跃), inactive(非活跃), suspended(暂停), banned(封禁)

            * `active` - 活跃
            * `inactive` - 非活跃
            * `suspended` - 暂停
            * `banned` - 封禁
        user_type:
          allOf:
          - $ref: '#/components/schemas/UserTypeEnum'
          title: 用户类型
          description: |-
            用户类型：regular(普通), premium(高级), vip(VIP), admin(管理员)

            * `regular` - 普通用户
            * `premium` - 高级用户
            * `vip` - VIP用户
            * `admin` - 管理员
    UserSession:
      type: object
      description: 用户会话序列化器
      properties:
        id:
          type: string
          readOnly: true
        user_id:
          type: string
          readOnly: true
        session_id:
          type: string
          readOnly: true
          title: 会话ID
        device_type:
          type: string
          title: 设备类型
          maxLength: 20
        device_info:
          readOnly: true
          title: 设备信息
        ip_address:
          type: string
          readOnly: true
          title: IP地址
        location:
          type: string
          readOnly: true
          title: 位置
        is_active:
          type: boolean
          title: 是否活跃
        is_current:
          type: string
          readOnly: true
        last_activity:
          type: string
          format: date-time
          readOnly: true
          title: 最后活动时间
        expires_at:
          type: string
          format: date-time
          readOnly: true
          title: 过期时间
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
      required:
      - device_type
    UserStatusEnum:
      enum:
      - active
      - inactive
      - suspended
      - banned
      type: string
      description: |-
        * `active` - 活跃
        * `inactive` - 非活跃
        * `suspended` - 暂停
        * `banned` - 封禁
    UserTypeEnum:
      enum:
      - regular
      - premium
      - vip
      - admin
      type: string
      description: |-
        * `regular` - 普通用户
        * `premium` - 高级用户
        * `vip` - VIP用户
        * `admin` - 管理员
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
    jwtAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
servers:
- url: http://localhost:8000
  description: 开发环境服务器
- url: https://api.soic.com
  description: 生产环境服务器
tags:
- name: 用户认证
  description: "\n            **用户认证系统** - `/api/v1/auth/`\n\n            提供完整的用户认证和授权功能：\n\
    \            - \U0001F510 用户注册与邮箱验证\n            - \U0001F511 用户登录与JWT令牌管理\n \
    \           - \U0001F504 密码重置与修改\n            - \U0001F464 用户资料管理与更新\n       \
    \     - \U0001F5BC️ 用户头像上传与管理\n            - \U0001F4F1 会话管理与设备绑定\n\n        \
    \    **认证方式**: Bearer Token\n            **路径前缀**: `/api/v1/auth/`\n         \
    \   "
  externalDocs:
    description: 用户认证详细文档
    url: https://docs.soic.com/auth
- name: 社交功能
  description: "\n            **社交功能系统** - `/api/v1/social/`\n\n            构建丰富的社交网络功能：\n\
    \            - \U0001F465 好友关系管理（添加、删除、查看）\n            - \U0001F441️ 关注/粉丝系统\n\
    \            - \U0001F3E2 群组创建与管理\n            - \U0001F3AF 社交活动推荐\n         \
    \   - \U0001F4CA 社交统计与分析\n            - \U0001F514 社交动态通知\n\n            **功能特色**:\
    \ 智能推荐、实时互动\n            **路径前缀**: `/api/v1/social/`\n            "
  externalDocs:
    description: 社交功能详细文档
    url: https://docs.soic.com/social
- name: 消息系统
  description: "\n            **消息通信系统** - `/api/v1/messaging/`\n\n            实现全方位的消息通信功能：\n\
    \            - \U0001F4AC 私聊消息发送与接收\n            - \U0001F465 群聊功能与管理\n      \
    \      - ⚡ 实时消息推送\n            - \U0001F4DA 消息历史记录查询\n            - \U0001F514\
    \ 在线状态管理\n            - \U0001F4CA 消息统计与分析\n\n            **技术特色**: WebSocket实时通信\n\
    \            **路径前缀**: `/api/v1/messaging/`\n            "
  externalDocs:
    description: 消息系统详细文档
    url: https://docs.soic.com/messaging
- name: 内容管理
  description: "\n            **内容管理系统** - `/api/v1/content/`\n\n            提供完整的内容创作与管理功能：\n\
    \            - \U0001F4DD 帖子发布与编辑\n            - \U0001F4AC 评论系统与互动\n        \
    \    - \U0001F3F7️ 内容分类与标签\n            - \U0001F50D 内容搜索与筛选\n            - ⚖️\
    \ 内容审核与管理\n            - \U0001F4CA 内容统计与分析\n\n            **内容类型**: 文本、图片、视频、链接\n\
    \            **路径前缀**: `/api/v1/content/`\n            "
  externalDocs:
    description: 内容管理详细文档
    url: https://docs.soic.com/content
- name: 经济系统
  description: "\n            **虚拟经济系统** - `/api/v1/economy/`\n\n            构建完整的虚拟经济生态：\n\
    \            - \U0001F4B0 虚拟货币管理\n            - \U0001F45B 用户钱包功能\n          \
    \  - \U0001F6D2 商品展示与购买\n            - \U0001F4B3 交易记录与管理\n            - \U0001F4C8\
    \ 订单处理流程\n            - \U0001F4CA 经济数据统计\n\n            **支付方式**: 虚拟货币、积分系统\n\
    \            **路径前缀**: `/api/v1/economy/`\n            "
  externalDocs:
    description: 经济系统详细文档
    url: https://docs.soic.com/economy
- name: 系统核心
  description: "\n            **系统核心功能** - `/api/v1/core/`\n\n            提供系统基础设施与监控功能：\n\
    \            - ❤️ 系统健康检查\n            - \U0001F4CA 系统运行统计\n            - ⚙️ 系统配置信息\n\
    \            - \U0001F4C8 性能监控数据\n            - \U0001F527 系统维护工具\n          \
    \  - \U0001F4CB API信息展示\n\n            **访问权限**: 部分接口需要管理员权限\n            **路径前缀**:\
    \ `/api/v1/core/`\n            "
  externalDocs:
    description: 系统核心详细文档
    url: https://docs.soic.com/core
externalDocs:
  description: 更多文档
  url: https://docs.soic.com
