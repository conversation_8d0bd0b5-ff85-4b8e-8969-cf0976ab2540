#!/usr/bin/env python
"""
Django 命令行管理工具
用于执行各种管理任务，如数据库迁移、创建用户、运行服务器等

常用命令：
- python manage.py runserver          # 启动开发服务器不稳定
- python manage.py makemigrations     # 创建数据库迁移文件
- python manage.py migrate            # 执行数据库迁移
- python manage.py createsuperuser    # 创建超级用户
- python manage.py collectstatic      # 收集静态文件
- python manage.py shell              # 进入Django交互式
- python manage.py test               # 运行测试
"""
import os
import sys

def main():
    """
    执行管理任务的主函数

    该函数负责：
    1. 设置Django配置模块
    2. 运行启动检查（针对runserver命令）
    3. 导入Django管理命令执行器
    4. 执行命令行传入的管理命令
    """
    # 设置默认的Django配置模块为本地开发环境
    # 如果环境变量中没有设置DJANGO_SETTINGS_MODULE，则使用本地开发配置
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

    try:
        # 导入Django的命令行执行函数
        # 这个函数负责解析命令行参数并执行相应的管理命令
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        # 如果导入失败，说明Django没有正确安装或虚拟环境没有激活
        raise ImportError(
            "无法导入Django。请确保Django已正确安装并且在PYTHONPATH环境变量中可用。"
            "您是否忘记激活虚拟环境了？"
        ) from exc

    # 如果是运行开发服务器，先进行启动检查
    if len(sys.argv) > 1 and sys.argv[1] == 'runserver':
        try:
            # 导入启动管理器
            from apps.core.startup import run_startup_checks

            # 运行启动检查
            success = run_startup_checks()

            if not success:
                print("\n❌ 启动检查失败，请修复问题后重试")
                sys.exit(1)

        except ImportError:
            # 如果无法导入启动检查模块，继续正常启动但给出警告
            print("⚠️  无法加载启动检查模块，跳过启动检查")
        except Exception as e:
            # 如果启动检查过程中出现异常，给出警告但继续启动
            print(f"⚠️  启动检查过程中出现异常: {str(e)}")
            print("继续启动服务器...")

    # 执行命令行参数指定的管理命令
    # sys.argv包含了所有命令行参数，第一个是脚本名，后面是具体的命令和参数
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    # 当直接运行此脚本时，调用main函数
    # 这是Python脚本的标准入口点模式
    main()