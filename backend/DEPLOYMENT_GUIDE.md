# SOIC 社交创新社区 - 生产环境部署指南

## 🎯 部署概述

本指南将帮助您将SOIC社交创新社区后端系统部署到生产环境。系统已经过全面优化，具备生产环境部署的所有条件。

## 📋 系统要求

### 服务器要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **CPU**: 最少2核，推荐4核+
- **内存**: 最少4GB，推荐8GB+
- **存储**: 最少20GB，推荐50GB+ SSD
- **网络**: 稳定的互联网连接

### 软件要求
- **Python**: 3.9+
- **数据库**: PostgreSQL 13+ (推荐) 或 MySQL 8.0+
- **缓存**: Redis 6.0+
- **Web服务器**: Nginx 1.18+
- **进程管理**: Gunicorn + Supervisor

## 🚀 部署步骤

### 1. 服务器准备

#### 1.1 更新系统
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

#### 1.2 安装基础软件
```bash
# Ubuntu/Debian
sudo apt install -y python3.9 python3.9-venv python3.9-dev \
    postgresql postgresql-contrib redis-server nginx \
    git curl wget supervisor

# CentOS/RHEL
sudo yum install -y python39 python39-devel postgresql-server \
    postgresql-contrib redis nginx git curl wget supervisor
```

### 2. 数据库配置

#### 2.1 PostgreSQL配置 (推荐)
```bash
# 初始化数据库
sudo postgresql-setup --initdb

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库和用户
sudo -u postgres psql
```

```sql
-- 在PostgreSQL中执行
CREATE DATABASE soic_prod;
CREATE USER soic_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE soic_prod TO soic_user;
ALTER USER soic_user CREATEDB;
\q
```

#### 2.2 Redis配置
```bash
# 启动Redis
sudo systemctl start redis
sudo systemctl enable redis

# 配置Redis (可选)
sudo nano /etc/redis/redis.conf
# 设置密码: requirepass your_redis_password
```

### 3. 应用部署

#### 3.1 创建部署用户
```bash
sudo useradd -m -s /bin/bash soic
sudo usermod -aG sudo soic
sudo su - soic
```

#### 3.2 克隆代码
```bash
cd /home/<USER>
git clone https://github.com/your-org/soic.git
cd soic/backend
```

#### 3.3 创建虚拟环境
```bash
python3.9 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. 环境配置

#### 4.1 创建环境变量文件
```bash
cp .env.example .env.production
nano .env.production
```

#### 4.2 配置环境变量
```bash
# .env.production 文件内容
DJANGO_SETTINGS_MODULE=config.settings.production
SECRET_KEY=your_very_long_and_secure_secret_key_here
DEBUG=False

# 数据库配置
DB_NAME=soic_prod
DB_USER=soic_user
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=5432

# Redis配置
REDIS_URL=redis://localhost:6379/1
REDIS_SESSION_URL=redis://localhost:6379/2
REDIS_API_URL=redis://localhost:6379/3

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
DEFAULT_FROM_EMAIL=<EMAIL>

# AWS S3配置 (可选)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_STORAGE_BUCKET_NAME=soic-media
AWS_S3_REGION_NAME=ap-northeast-1

# 域名配置
ALLOWED_HOSTS=api.soic.com,soic.com,www.soic.com
CORS_ALLOWED_ORIGINS=https://soic.com,https://www.soic.com

# 管理员配置
ADMIN_EMAIL=<EMAIL>
SERVER_EMAIL=<EMAIL>
```

### 5. 数据库迁移

```bash
# 加载环境变量
source .env.production

# 运行迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic --noinput
```

### 6. Gunicorn配置

#### 6.1 创建Gunicorn配置文件
```bash
nano gunicorn.conf.py
```

```python
# gunicorn.conf.py
import multiprocessing

bind = "127.0.0.1:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
daemon = False
user = "soic"
group = "soic"
tmp_upload_dir = None
errorlog = "/home/<USER>/soic/backend/logs/gunicorn_error.log"
accesslog = "/home/<USER>/soic/backend/logs/gunicorn_access.log"
loglevel = "info"
```

#### 6.2 创建启动脚本
```bash
nano start_gunicorn.sh
chmod +x start_gunicorn.sh
```

```bash
#!/bin/bash
# start_gunicorn.sh
cd /home/<USER>/soic/backend
source venv/bin/activate
source .env.production
exec gunicorn config.wsgi:application -c gunicorn.conf.py
```

### 7. Supervisor配置

```bash
sudo nano /etc/supervisor/conf.d/soic.conf
```

```ini
[program:soic]
command=/home/<USER>/soic/backend/start_gunicorn.sh
directory=/home/<USER>/soic/backend
user=soic
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/soic/backend/logs/supervisor.log
environment=PATH="/home/<USER>/soic/backend/venv/bin"
```

```bash
# 重新加载Supervisor配置
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start soic
```

### 8. Nginx配置

```bash
sudo nano /etc/nginx/sites-available/soic
```

```nginx
server {
    listen 80;
    server_name api.soic.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.soic.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/soic.crt;
    ssl_certificate_key /etc/ssl/private/soic.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";
    
    # 静态文件
    location /static/ {
        alias /home/<USER>/soic/backend/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /media/ {
        alias /home/<USER>/soic/backend/media/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # API请求
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 限制请求大小
    client_max_body_size 10M;
    
    # 日志
    access_log /var/log/nginx/soic_access.log;
    error_log /var/log/nginx/soic_error.log;
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/soic /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🔒 SSL证书配置

### 使用Let's Encrypt (推荐)
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d api.soic.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和维护

### 1. 系统监控
```bash
# 检查服务状态
sudo supervisorctl status soic
sudo systemctl status nginx
sudo systemctl status postgresql
sudo systemctl status redis

# 查看日志
tail -f /home/<USER>/soic/backend/logs/django.log
tail -f /home/<USER>/soic/backend/logs/gunicorn_error.log
tail -f /var/log/nginx/soic_error.log
```

### 2. 健康检查
```bash
# 运行系统健康检查
cd /home/<USER>/soic/backend
source venv/bin/activate
python scripts/system_health_check.py
```

### 3. 备份策略
```bash
# 数据库备份脚本
nano backup_db.sh
chmod +x backup_db.sh
```

```bash
#!/bin/bash
# backup_db.sh
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -h localhost -U soic_user soic_prod > $BACKUP_DIR/db_backup_$DATE.sql

# 媒体文件备份
tar -czf $BACKUP_DIR/media_backup_$DATE.tar.gz /home/<USER>/soic/backend/media/

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

## 🚨 故障排除

### 常见问题
1. **502 Bad Gateway**: 检查Gunicorn是否运行
2. **数据库连接错误**: 检查数据库配置和权限
3. **静态文件404**: 运行 `collectstatic` 命令
4. **SSL证书错误**: 检查证书路径和权限

### 性能优化
1. **数据库优化**: 定期运行 `VACUUM` 和 `ANALYZE`
2. **缓存预热**: 使用 `python manage.py shell` 运行缓存预热脚本
3. **日志轮转**: 配置logrotate管理日志文件

## 📞 支持

如需技术支持，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.soic.com
- GitHub: https://github.com/your-org/soic

---

**部署完成后，您的SOIC社交创新社区将在 https://api.soic.com 上运行！** 🎉
