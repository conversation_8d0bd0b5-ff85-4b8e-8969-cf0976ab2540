# SOIC 社交创新社区 - 生产环境配置模板
# 复制此文件为 .env.production 并修改相应的值

# =============================================================================
# 基础配置
# =============================================================================

# Django设置模块 - 生产环境
DJANGO_SETTINGS_MODULE=config.settings.production

# 安全密钥 - 必须设置为复杂的随机字符串
# 生成命令: python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
SECRET_KEY=your-very-long-and-secure-secret-key-here-change-this

# 调试模式 - 生产环境必须为False
DEBUG=False

# 允许的主机 - 配置实际域名
ALLOWED_HOSTS=api.soic.com,soic.com,www.soic.com

# =============================================================================
# 数据库配置 - PostgreSQL (推荐)
# =============================================================================

DB_NAME=soic_prod
DB_USER=soic_user
DB_PASSWORD=your_secure_database_password
DB_HOST=localhost
DB_PORT=5432

# =============================================================================
# 缓存配置 - Redis
# =============================================================================

REDIS_URL=redis://localhost:6379/1
REDIS_SESSION_URL=redis://localhost:6379/2
REDIS_API_URL=redis://localhost:6379/3

# Redis密码 (如果设置了密码)
# REDIS_PASSWORD=your_redis_password

# =============================================================================
# 邮件配置
# =============================================================================

EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
DEFAULT_FROM_EMAIL=<EMAIL>
SERVER_EMAIL=<EMAIL>

# =============================================================================
# 文件存储配置 - AWS S3
# =============================================================================

AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_STORAGE_BUCKET_NAME=soic-media
AWS_S3_REGION_NAME=ap-northeast-1

# =============================================================================
# 安全配置
# =============================================================================

# HTTPS配置
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# Cookie安全配置
CSRF_COOKIE_SECURE=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_HTTPONLY=True
SESSION_COOKIE_HTTPONLY=True

# CORS配置
CORS_ALLOWED_ORIGINS=https://soic.com,https://www.soic.com,https://app.soic.com
CORS_ALLOW_CREDENTIALS=True

# =============================================================================
# 管理员配置
# =============================================================================

ADMIN_EMAIL=<EMAIL>
ADMIN_NAME=SOIC管理员

# =============================================================================
# 监控配置
# =============================================================================

# Sentry错误监控 (可选)
# SENTRY_DSN=https://<EMAIL>/project-id

# 日志级别
LOG_LEVEL=INFO

# =============================================================================
# 备份配置
# =============================================================================

BACKUP_S3_BUCKET=soic-backups
BACKUP_RETENTION_DAYS=30

# =============================================================================
# 第三方服务配置 (可选)
# =============================================================================

# 微信配置
# WECHAT_APP_ID=your_wechat_app_id
# WECHAT_APP_SECRET=your_wechat_app_secret

# 支付宝配置
# ALIPAY_APP_ID=your_alipay_app_id
# ALIPAY_PRIVATE_KEY=your_alipay_private_key

# 短信服务配置
# SMS_ACCESS_KEY_ID=your_sms_access_key
# SMS_ACCESS_KEY_SECRET=your_sms_secret_key

# =============================================================================
# 重要提醒
# =============================================================================

# 1. 必须修改的配置:
#    - SECRET_KEY: 设置为复杂的随机字符串
#    - 数据库密码: 设置强密码
#    - 邮件配置: 配置实际的SMTP服务器
#    - AWS配置: 配置实际的AWS凭证

# 2. 安全注意事项:
#    - 不要将此文件提交到版本控制
#    - 定期轮换密钥和密码
#    - 使用环境变量或密钥管理服务

# 3. 部署后验证:
#    - 运行健康检查: python scripts/system_health_check.py
#    - 检查SSL证书: https://www.ssllabs.com/ssltest/
#    - 验证API文档: https://api.soic.com/api/docs/
