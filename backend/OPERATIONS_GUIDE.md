# SOIC 社交创新社区 - 运维监控指南

## 🎯 概述

本指南提供SOIC社交创新社区生产环境的日常运维、监控和故障处理方法。

## 📊 系统监控

### 1. 健康检查

#### 自动健康检查
```bash
# 运行完整的系统健康检查
cd /home/<USER>/soic/backend
source venv/bin/activate
python scripts/system_health_check.py

# 设置定时任务 (每5分钟检查一次)
crontab -e
# 添加: */5 * * * * /home/<USER>/soic/backend/venv/bin/python /home/<USER>/soic/backend/scripts/system_health_check.py
```

#### API健康检查端点
```bash
# 检查API健康状态
curl -s https://api.soic.com/api/v1/core/health/ | jq

# 预期响应
{
  "status": "healthy",
  "timestamp": "2025-01-01T00:00:00Z",
  "environment": "production",
  "checks": {
    "database": "ok",
    "cache": "ok",
    "users": "ok",
    "social": "ok",
    "messaging": "ok",
    "content": "ok",
    "economy": "ok"
  }
}
```

### 2. 服务状态监控

#### 检查核心服务
```bash
# 检查应用服务
sudo supervisorctl status soic

# 检查Web服务器
sudo systemctl status nginx

# 检查数据库
sudo systemctl status postgresql

# 检查缓存
sudo systemctl status redis

# 检查所有服务
sudo systemctl status nginx postgresql redis supervisor
```

#### 检查端口占用
```bash
# 检查关键端口
sudo netstat -tlnp | grep -E ':(80|443|8000|5432|6379)'

# 或使用ss命令
sudo ss -tlnp | grep -E ':(80|443|8000|5432|6379)'
```

### 3. 性能监控

#### 系统资源监控
```bash
# CPU和内存使用情况
htop

# 磁盘使用情况
df -h

# 磁盘I/O
iostat -x 1

# 网络连接
ss -tuln
```

#### 数据库性能监控
```bash
# PostgreSQL连接数
sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity;"

# 慢查询监控
sudo -u postgres psql -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# 数据库大小
sudo -u postgres psql -c "SELECT pg_size_pretty(pg_database_size('soic_prod'));"
```

#### Redis性能监控
```bash
# Redis信息
redis-cli info

# Redis内存使用
redis-cli info memory

# Redis连接数
redis-cli info clients
```

## 📋 日志管理

### 1. 日志位置
```bash
# 应用日志
/home/<USER>/soic/backend/logs/django.log
/home/<USER>/soic/backend/logs/django_error.log

# Gunicorn日志
/home/<USER>/soic/backend/logs/gunicorn_access.log
/home/<USER>/soic/backend/logs/gunicorn_error.log

# Nginx日志
/var/log/nginx/soic_access.log
/var/log/nginx/soic_error.log

# 系统日志
/var/log/syslog
/var/log/auth.log
```

### 2. 日志查看命令
```bash
# 实时查看应用日志
tail -f /home/<USER>/soic/backend/logs/django.log

# 查看错误日志
tail -f /home/<USER>/soic/backend/logs/django_error.log

# 查看Nginx访问日志
tail -f /var/log/nginx/soic_access.log

# 搜索特定错误
grep -i "error" /home/<USER>/soic/backend/logs/django.log | tail -20

# 查看特定时间段的日志
grep "2025-01-01 10:" /home/<USER>/soic/backend/logs/django.log
```

### 3. 日志轮转配置
```bash
# 创建logrotate配置
sudo nano /etc/logrotate.d/soic
```

```
/home/<USER>/soic/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 soic soic
    postrotate
        sudo supervisorctl restart soic
    endscript
}
```

## 🔧 维护操作

### 1. 应用更新部署

#### 标准更新流程
```bash
# 1. 备份数据库
sudo -u postgres pg_dump soic_prod > /home/<USER>/backups/db_backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 切换到应用目录
cd /home/<USER>/soic/backend
source venv/bin/activate

# 3. 拉取最新代码
git pull origin main

# 4. 安装新依赖
pip install -r requirements.txt

# 5. 运行数据库迁移
python manage.py migrate

# 6. 收集静态文件
python manage.py collectstatic --noinput

# 7. 重启应用
sudo supervisorctl restart soic

# 8. 验证部署
python scripts/system_health_check.py
```

#### 回滚操作
```bash
# 1. 回滚代码
git reset --hard HEAD~1

# 2. 回滚数据库 (如果需要)
sudo -u postgres psql soic_prod < /home/<USER>/backups/db_backup_YYYYMMDD_HHMMSS.sql

# 3. 重启应用
sudo supervisorctl restart soic
```

### 2. 数据库维护

#### 定期维护任务
```bash
# 数据库优化
sudo -u postgres psql soic_prod -c "VACUUM ANALYZE;"

# 重建索引
sudo -u postgres psql soic_prod -c "REINDEX DATABASE soic_prod;"

# 清理过期会话
python manage.py clearsessions

# 清理过期缓存
redis-cli FLUSHDB
```

#### 数据库备份
```bash
# 创建备份脚本
nano /home/<USER>/scripts/backup_database.sh
chmod +x /home/<USER>/scripts/backup_database.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# 数据库备份
sudo -u postgres pg_dump soic_prod | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# 上传到S3 (可选)
aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://soic-backups/database/

# 清理本地旧备份 (保留7天)
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: db_backup_$DATE.sql.gz"
```

### 3. 缓存管理

#### 缓存操作
```bash
# 清空所有缓存
redis-cli FLUSHALL

# 清空特定数据库
redis-cli -n 1 FLUSHDB

# 查看缓存使用情况
redis-cli info memory

# 查看缓存键
redis-cli --scan --pattern "soic_*"
```

#### 缓存预热
```bash
# 运行缓存预热脚本
python manage.py shell
```

```python
# 在Django shell中执行
from apps.core.cache import warm_up_cache
warm_up_cache()
```

## 🚨 故障处理

### 1. 常见问题诊断

#### 服务无法访问
```bash
# 1. 检查Nginx状态
sudo systemctl status nginx
sudo nginx -t

# 2. 检查应用状态
sudo supervisorctl status soic

# 3. 检查端口占用
sudo netstat -tlnp | grep :8000

# 4. 查看错误日志
tail -f /var/log/nginx/soic_error.log
tail -f /home/<USER>/soic/backend/logs/gunicorn_error.log
```

#### 数据库连接问题
```bash
# 1. 检查PostgreSQL状态
sudo systemctl status postgresql

# 2. 测试数据库连接
sudo -u postgres psql soic_prod -c "SELECT 1;"

# 3. 检查连接数
sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity;"

# 4. 查看数据库日志
sudo tail -f /var/log/postgresql/postgresql-13-main.log
```

#### 缓存问题
```bash
# 1. 检查Redis状态
sudo systemctl status redis

# 2. 测试Redis连接
redis-cli ping

# 3. 查看Redis日志
sudo tail -f /var/log/redis/redis-server.log
```

### 2. 性能问题处理

#### 高CPU使用率
```bash
# 1. 查看进程CPU使用情况
top -p $(pgrep -f gunicorn)

# 2. 查看慢查询
sudo -u postgres psql soic_prod -c "SELECT query, mean_time FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 5;"

# 3. 重启应用
sudo supervisorctl restart soic
```

#### 内存不足
```bash
# 1. 查看内存使用情况
free -h

# 2. 查看进程内存使用
ps aux --sort=-%mem | head -10

# 3. 清理缓存
echo 3 | sudo tee /proc/sys/vm/drop_caches
```

#### 磁盘空间不足
```bash
# 1. 查看磁盘使用情况
df -h

# 2. 查找大文件
sudo find / -type f -size +100M -exec ls -lh {} \;

# 3. 清理日志文件
sudo journalctl --vacuum-time=7d
sudo find /var/log -name "*.log" -mtime +7 -delete

# 4. 清理旧备份
find /home/<USER>/backups -mtime +30 -delete
```

## 📈 性能优化

### 1. 数据库优化
```bash
# 更新统计信息
sudo -u postgres psql soic_prod -c "ANALYZE;"

# 检查索引使用情况
sudo -u postgres psql soic_prod -c "SELECT schemaname,tablename,attname,n_distinct,correlation FROM pg_stats WHERE tablename='your_table';"
```

### 2. 缓存优化
```bash
# 调整Redis配置
sudo nano /etc/redis/redis.conf

# 重要配置项:
# maxmemory 2gb
# maxmemory-policy allkeys-lru
# save 900 1
```

### 3. Web服务器优化
```bash
# 调整Nginx配置
sudo nano /etc/nginx/sites-available/soic

# 重要配置项:
# worker_processes auto;
# worker_connections 1024;
# keepalive_timeout 65;
# gzip on;
```

## 📞 应急联系

### 紧急情况处理
1. **服务完全不可用**: 立即重启所有服务
2. **数据库问题**: 联系数据库管理员
3. **安全事件**: 立即隔离受影响系统
4. **数据丢失**: 立即停止服务，从备份恢复

### 联系信息
- **技术负责人**: <EMAIL>
- **运维团队**: <EMAIL>
- **紧急热线**: +86-xxx-xxxx-xxxx

---

**定期执行运维检查清单，确保系统稳定运行！** 🎯
