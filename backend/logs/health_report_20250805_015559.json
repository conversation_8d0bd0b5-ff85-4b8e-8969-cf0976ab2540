{"timestamp": "2025-08-05T01:55:59.503707", "overall_score": 84.4, "status": "良好 👍", "checks_performed": 5, "elapsed_time": 0.03, "details": {"database": {"default": {"vendor": "mysql", "response_time_ms": 24.55, "status": "healthy"}}, "cache": {"default": {"backend": "<PERSON><PERSON><PERSON><PERSON><PERSON>ache", "write_time_ms": 0.0, "read_time_ms": 0.0, "data_integrity": true, "status": "healthy"}}, "security": {"checks": {"DEBUG关闭": false, "SECRET_KEY长度": true, "ALLOWED_HOSTS配置": true, "SECURE_SSL_REDIRECT": false, "SECURE_HSTS_SECONDS": false, "CSRF_COOKIE_SECURE": false, "SESSION_COOKIE_SECURE": false, "X_FRAME_OPTIONS": true}, "score": 37.5, "passed": 3, "total": 8, "status": "needs_improvement"}, "application": {"user_count": 4, "apps": {"users": {"loaded": true, "models_count": 0, "status": "healthy", "name": "apps.users", "note": "配置获取异常: object of type 'generator' has no len()"}, "social": {"loaded": true, "models_count": 0, "status": "healthy", "name": "apps.social", "note": "配置获取异常: object of type 'generator' has no len()"}, "messaging": {"loaded": true, "models_count": 0, "status": "healthy", "name": "apps.messaging", "note": "配置获取异常: object of type 'generator' has no len()"}, "content": {"loaded": true, "models_count": 0, "status": "healthy", "name": "apps.content", "note": "配置获取异常: object of type 'generator' has no len()"}, "economy": {"loaded": true, "models_count": 0, "status": "healthy", "name": "apps.economy", "note": "配置获取异常: object of type 'generator' has no len()"}, "core": {"loaded": true, "models_count": 0, "status": "healthy", "name": "apps.core", "note": "配置获取异常: object of type 'generator' has no len()"}}, "apps_loaded": 6, "total_apps": 6}, "filesystem": {"logs": {"exists": true, "is_dir": true, "writable": true, "path": "F:\\lib_code\\soic\\backend\\logs"}, "static": {"exists": true, "is_dir": true, "writable": true, "path": "F:\\lib_code\\soic\\backend\\static"}, "media": {"exists": true, "is_dir": true, "writable": true, "path": "F:\\lib_code\\soic\\backend\\media"}, "templates": {"exists": true, "is_dir": true, "writable": true, "path": "F:\\lib_code\\soic\\backend\\templates"}}}}