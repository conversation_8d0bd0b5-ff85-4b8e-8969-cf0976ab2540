{"timestamp": "2025-08-05T01:54:32.914555", "overall_score": 59.4, "status": "需要改进 💥", "checks_performed": 5, "elapsed_time": 0.01, "details": {"database": {"default": {"vendor": "mysql", "response_time_ms": 5.75, "status": "healthy"}}, "cache": {"default": {"backend": "<PERSON><PERSON><PERSON><PERSON><PERSON>ache", "write_time_ms": 0.0, "read_time_ms": 0.0, "data_integrity": true, "status": "healthy"}}, "security": {"checks": {"DEBUG关闭": false, "SECRET_KEY长度": true, "ALLOWED_HOSTS配置": true, "SECURE_SSL_REDIRECT": false, "SECURE_HSTS_SECONDS": false, "CSRF_COOKIE_SECURE": false, "SESSION_COOKIE_SECURE": false, "X_FRAME_OPTIONS": true}, "score": 37.5, "passed": 3, "total": 8, "status": "needs_improvement"}, "application": {"user_count": 4, "apps": {"users": {"loaded": false, "error": "object of type 'generator' has no len()", "status": "error"}, "social": {"loaded": false, "error": "object of type 'generator' has no len()", "status": "error"}, "messaging": {"loaded": false, "error": "object of type 'generator' has no len()", "status": "error"}, "content": {"loaded": false, "error": "object of type 'generator' has no len()", "status": "error"}, "economy": {"loaded": false, "error": "object of type 'generator' has no len()", "status": "error"}, "core": {"loaded": false, "error": "object of type 'generator' has no len()", "status": "error"}}, "apps_loaded": 0, "total_apps": 6}, "filesystem": {"logs": {"exists": true, "is_dir": true, "writable": true, "path": "F:\\lib_code\\soic\\backend\\logs"}, "static": {"exists": true, "is_dir": true, "writable": true, "path": "F:\\lib_code\\soic\\backend\\static"}, "media": {"exists": true, "is_dir": true, "writable": true, "path": "F:\\lib_code\\soic\\backend\\media"}, "templates": {"exists": true, "is_dir": true, "writable": true, "path": "F:\\lib_code\\soic\\backend\\templates"}}}}