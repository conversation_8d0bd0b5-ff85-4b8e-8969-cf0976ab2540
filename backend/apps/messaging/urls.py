"""
消息URL路由配置 - 独立的消息域路由
职责：定义消息相关的API路由，支持微服务化部署
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ConversationViewSet,
    MessageViewSet,
    OnlineStatusViewSet,
    MessagingStatsViewSet
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'conversations', ConversationViewSet, basename='conversation')
router.register(r'messages', MessageViewSet, basename='message')
router.register(r'online-status', OnlineStatusViewSet, basename='onlinestatus')
router.register(r'messaging-stats', MessagingStatsViewSet, basename='messagingstats')

# URL模式
urlpatterns = [
    # API路由 - 不需要重复的api/v1/前缀
    path('', include(router.urls)),
]

# 应用命名空间
app_name = 'messaging'
