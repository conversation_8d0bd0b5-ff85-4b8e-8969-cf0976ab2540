"""
消息视图控制器 - 薄层HTTP处理
职责：处理消息相关的HTTP请求响应，调用业务服务，返回标准化响应
"""

import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes

from apps.core.responses import APIResponse, SuccessMessages
from apps.core.exceptions import handle_exceptions
from .services import MessagingService
from .serializers import (
    MessageCreateSerializer,
    ConversationCreateSerializer,
    MessageReactionSerializer,
    OnlineStatusSerializer
)

logger = logging.getLogger(__name__)


@extend_schema_view(
    list=extend_schema(
        summary="获取会话列表",
        description="获取当前用户的所有会话列表，包括私聊和群聊",
        tags=["消息系统"]
    ),
    create=extend_schema(
        summary="创建会话",
        description="创建新的会话（私聊或群聊）",
        tags=["消息系统"]
    ),
    retrieve=extend_schema(
        summary="获取会话详情",
        description="获取指定会话的详细信息和消息历史",
        tags=["消息系统"]
    ),
    update=extend_schema(
        summary="更新会话设置",
        description="更新会话设置，如免打扰、置顶等",
        tags=["消息系统"]
    ),
    destroy=extend_schema(
        summary="删除会话",
        description="删除会话（仅删除自己的会话记录）",
        tags=["消息系统"]
    )
)
class ConversationViewSet(viewsets.ViewSet):
    """
    会话视图集

    职责：
    1. 处理会话相关的HTTP请求
    2. 调用消息业务服务
    3. 返回标准化响应
    """

    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.messaging_service = MessagingService()
    def create(self, request: Request) -> Response:
        """
        创建会话
        
        POST /api/v1/conversations/
        """
        logger.info(f"创建会话请求: {request.user.username}")
        
        # 数据验证
        serializer = ConversationCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.messaging_service.create_conversation(
            str(request.user.uuid),
            serializer.validated_data
        )
        
        return APIResponse.created(
            data=result,
            message="会话创建成功"
        )
    
    @action(detail=False, methods=['get'])
    def list_conversations(self, request: Request) -> Response:
        """
        获取用户会话列表
        
        GET /api/v1/conversations/list_conversations/
        """
        # 调用业务服务
        result = self.messaging_service.get_user_conversations(str(request.user.uuid))
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['get'])
    def messages(self, request: Request, pk=None) -> Response:
        """
        获取会话消息列表
        
        GET /api/v1/conversations/{id}/messages/?page=1&page_size=50
        """
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 50)), 100)
        
        # 调用业务服务
        result = self.messaging_service.get_conversation_messages(
            str(request.user.uuid),
            pk,
            page,
            page_size
        )
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['get'])
    def statistics(self, request: Request, pk=None) -> Response:
        """
        获取会话统计信息
        
        GET /api/v1/conversations/{id}/statistics/
        """
        # 调用业务服务
        result = self.messaging_service.get_conversation_statistics(
            str(request.user.uuid),
            pk
        )
        
        return APIResponse.success(data=result)


@extend_schema_view(
    list=extend_schema(
        summary="获取消息列表",
        description="获取指定会话中的消息列表，支持分页和历史消息查询",
        tags=["消息系统"]
    ),
    create=extend_schema(
        summary="发送消息",
        description="在指定会话中发送新消息",
        tags=["消息系统"]
    ),
    retrieve=extend_schema(
        summary="获取消息详情",
        description="获取指定消息的详细信息",
        tags=["消息系统"]
    ),
    update=extend_schema(
        summary="编辑消息",
        description="编辑已发送的消息（限时编辑）",
        tags=["消息系统"]
    ),
    destroy=extend_schema(
        summary="删除消息",
        description="删除消息（撤回消息）",
        tags=["消息系统"]
    )
)
class MessageViewSet(viewsets.ViewSet):
    """消息视图集"""

    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.messaging_service = MessagingService()
    def create(self, request: Request) -> Response:
        """
        发送消息
        
        POST /api/v1/messages/
        """
        logger.info(f"发送消息请求: {request.user.username}")
        
        # 数据验证
        serializer = MessageCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.messaging_service.send_message(
            str(request.user.uuid),
            serializer.validated_data['conversation_id'],
            serializer.validated_data
        )
        
        return APIResponse.created(
            data=result,
            message="消息发送成功"
        )
    
    @action(detail=True, methods=['post'])
    def mark_read(self, request: Request, pk=None) -> Response:
        """
        标记消息为已读
        
        POST /api/v1/messages/{id}/mark_read/
        """
        # 调用业务服务
        result = self.messaging_service.mark_message_as_read(
            str(request.user.uuid),
            pk
        )
        
        return APIResponse.success(
            data=result,
            message="消息已标记为已读"
        )
    
    @action(detail=True, methods=['post'])
    def add_reaction(self, request: Request, pk=None) -> Response:
        """
        添加消息反应
        
        POST /api/v1/messages/{id}/add_reaction/
        """
        # 数据验证
        serializer = MessageReactionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.messaging_service.add_message_reaction(
            str(request.user.uuid),
            pk,
            serializer.validated_data['reaction_type']
        )
        
        return APIResponse.success(
            data=result,
            message="反应添加成功"
        )
    
    @action(detail=False, methods=['get'])
    def search(self, request: Request) -> Response:
        """
        搜索消息
        
        GET /api/v1/messages/search/?q=keyword&conversation_id=xxx&page=1&page_size=20
        """
        query = request.query_params.get('q', '').strip()
        if not query:
            return APIResponse.validation_error(
                errors={'q': ['搜索关键词不能为空']},
                message="参数验证失败"
            )
        
        conversation_id = request.query_params.get('conversation_id')
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 20)), 50)
        
        # 调用业务服务
        result = self.messaging_service.search_messages(
            str(request.user.uuid),
            query,
            conversation_id,
            page,
            page_size
        )
        
        return APIResponse.success(data=result)


@extend_schema_view(
    list=extend_schema(
        summary="获取在线状态列表",
        description="获取好友和群组成员的在线状态",
        tags=["消息系统"]
    ),
    create=extend_schema(
        summary="更新在线状态",
        description="更新当前用户的在线状态",
        tags=["消息系统"]
    ),
    retrieve=extend_schema(
        summary="获取用户在线状态",
        description="获取指定用户的在线状态",
        tags=["消息系统"]
    )
)
class OnlineStatusViewSet(viewsets.ViewSet):
    """在线状态视图集"""

    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.messaging_service = MessagingService()
    
    @action(detail=False, methods=['post'])
    def update_status(self, request: Request) -> Response:
        """
        更新在线状态
        
        POST /api/v1/online-status/update_status/
        """
        # 数据验证
        serializer = OnlineStatusSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.messaging_service.update_online_status(
            str(request.user.uuid),
            serializer.validated_data['status'],
            serializer.validated_data.get('device_info', {})
        )
        
        return APIResponse.success(
            data=result,
            message="状态更新成功"
        )
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60))  # 缓存1分钟
    def friends_status(self, request: Request) -> Response:
        """
        获取好友在线状态
        
        GET /api/v1/online-status/friends_status/
        """
        from apps.social.models import Friendship
        from .models import UserOnlineStatus
        
        # 获取用户的好友
        friendships = Friendship.objects.filter(
            models.Q(requester=request.user) | models.Q(addressee=request.user),
            status='accepted'
        ).select_related('requester', 'addressee')
        
        friend_ids = []
        for friendship in friendships:
            friend = friendship.addressee if friendship.requester == request.user else friendship.requester
            friend_ids.append(friend.id)
        
        # 获取好友在线状态
        online_statuses = UserOnlineStatus.objects.filter(
            user_id__in=friend_ids
        ).select_related('user')
        
        status_data = []
        for online_status in online_statuses:
            status_data.append({
                'user': {
                    'id': str(online_status.user.uuid),
                    'username': online_status.user.username,
                    'display_name': online_status.user.display_name
                },
                'status': online_status.status,
                'status_message': online_status.status_message,
                'last_seen_at': online_status.last_seen_at.isoformat(),
                'device_type': online_status.device_type
            })
        
        return APIResponse.success(data=status_data)


@extend_schema_view(
    list=extend_schema(
        summary="获取消息统计",
        description="获取用户的消息统计数据，包括消息数量、会话数量等",
        tags=["消息系统"]
    )
)
class MessagingStatsViewSet(viewsets.ViewSet):
    """消息统计视图集"""

    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def overview(self, request: Request) -> Response:
        """
        获取消息统计概览
        
        GET /api/v1/messaging-stats/overview/
        """
        from .models import Conversation, Message, ConversationParticipant
        from django.db.models import Count, Q
        
        # 统计用户的会话数量
        conversation_count = ConversationParticipant.objects.filter(
            user=request.user,
            status='active'
        ).count()
        
        # 统计未读消息数量
        total_unread = ConversationParticipant.objects.filter(
            user=request.user,
            status='active'
        ).aggregate(total_unread=models.Sum('unread_count'))['total_unread'] or 0
        
        # 统计发送的消息数量
        sent_messages = Message.objects.filter(
            sender=request.user,
            status__in=['sent', 'delivered', 'read']
        ).count()
        
        # 统计接收的消息数量
        received_messages = Message.objects.filter(
            conversation__participants=request.user,
            status__in=['sent', 'delivered', 'read']
        ).exclude(sender=request.user).count()
        
        # 统计各类型会话数量
        conversation_types = ConversationParticipant.objects.filter(
            user=request.user,
            status='active'
        ).values('conversation__conversation_type').annotate(
            count=Count('id')
        )
        
        return APIResponse.success(data={
            'user_id': str(request.user.uuid),
            'conversation_count': conversation_count,
            'total_unread': total_unread,
            'sent_messages': sent_messages,
            'received_messages': received_messages,
            'conversation_types': {
                item['conversation__conversation_type']: item['count'] 
                for item in conversation_types
            }
        })


# WebSocket视图（用于实时通信）
class MessagingWebSocketView:
    """
    消息WebSocket视图
    
    职责：处理WebSocket连接和实时消息传输
    注意：这是一个示例结构，实际实现需要使用Django Channels
    """
    
    def __init__(self):
        self.messaging_service = MessagingService()
    
    async def connect(self, websocket, user_id: str):
        """WebSocket连接"""
        # 更新用户在线状态
        await self.messaging_service.update_online_status(
            user_id, 
            'online',
            {'connection_type': 'websocket'}
        )
        
        # 加入用户房间
        await self.join_user_room(websocket, user_id)
    
    async def disconnect(self, websocket, user_id: str):
        """WebSocket断开连接"""
        # 更新用户离线状态
        await self.messaging_service.update_online_status(
            user_id,
            'offline'
        )
        
        # 离开用户房间
        await self.leave_user_room(websocket, user_id)
    
    async def receive_message(self, websocket, data: dict):
        """接收WebSocket消息"""
        message_type = data.get('type')
        
        if message_type == 'send_message':
            # 发送消息
            result = await self.messaging_service.send_message(
                data['sender_id'],
                data['conversation_id'],
                data['message_data']
            )
            
            # 广播消息给会话参与者
            await self.broadcast_to_conversation(
                data['conversation_id'],
                {
                    'type': 'new_message',
                    'message': result
                }
            )
        
        elif message_type == 'typing':
            # 输入状态
            await self.broadcast_to_conversation(
                data['conversation_id'],
                {
                    'type': 'typing',
                    'user_id': data['user_id'],
                    'is_typing': data['is_typing']
                }
            )
        
        elif message_type == 'mark_read':
            # 标记已读
            result = await self.messaging_service.mark_message_as_read(
                data['user_id'],
                data['message_id']
            )
            
            # 通知发送者消息已读
            await self.send_to_user(
                data['sender_id'],
                {
                    'type': 'message_read',
                    'message_id': data['message_id'],
                    'reader_id': data['user_id']
                }
            )
    
    async def join_user_room(self, websocket, user_id: str):
        """加入用户房间"""
        # 实现用户房间逻辑
        pass
    
    async def leave_user_room(self, websocket, user_id: str):
        """离开用户房间"""
        # 实现离开房间逻辑
        pass
    
    async def broadcast_to_conversation(self, conversation_id: str, message: dict):
        """向会话参与者广播消息"""
        # 实现会话广播逻辑
        pass
    
    async def send_to_user(self, user_id: str, message: dict):
        """向特定用户发送消息"""
        # 实现用户消息发送逻辑
        pass
