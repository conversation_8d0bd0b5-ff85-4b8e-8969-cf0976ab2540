"""
用户URL路由配置 - 独立的用户域路由
职责：定义用户相关的API路由，支持微服务化部署
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import UserViewSet, UserProfileViewSet, UserAvatarViewSet, UserSessionViewSet, RegisterView, RegisterTestView, register_user_standalone

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'users', UserViewSet, basename='user')
router.register(r'profiles', UserProfileViewSet, basename='userprofile')
router.register(r'avatars', UserAvatarViewSet, basename='useravatar')
router.register(r'sessions', UserSessionViewSet, basename='usersession')

# URL模式
urlpatterns = [
    # API路由 - 不需要重复的api/v1/前缀，因为在主urls.py中已经包含
    path('', include(router.urls)),

    # 用户注册路由 - 独立的APIView实现
    path('register/', RegisterView.as_view(), name='user-register'),

    # 测试路由
    path('test-register/', RegisterTestView.as_view(), name='test-register'),

    # 独立注册路由 - 完全绕过DRF
    path('register-standalone/', register_user_standalone, name='register-standalone'),

    # 自定义路由（如果需要）
    # path('auth/', include('rest_framework.urls')),
]

# 应用命名空间
app_name = 'users'