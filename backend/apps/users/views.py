"""
用户视图控制器 - 薄层HTTP处理
职责：处理HTTP请求响应，调用业务服务，返回标准化响应
"""

import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.http import JsonResponse
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
import json
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.views.decorators.vary import vary_on_headers
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes

from apps.core.responses import APIResponse, SuccessMessages, ErrorCodes
# from apps.core.exceptions import handle_exceptions  # 暂时移除
from apps.core.dependencies import inject
from .services import UserService
from .serializers import (
    UserRegistrationSerializer,
    UserLoginSerializer,
    UserSerializer,
    UserProfileSerializer,
    UserAvatarSerializer,
    UserSessionSerializer
)
from .permissions import IsOwnerOrReadOnly, IsProfileOwner

logger = logging.getLogger(__name__)


@extend_schema_view(
    list=extend_schema(
        summary="获取用户列表",
        description="获取系统中的用户列表，支持分页和筛选",
        tags=["用户认证"]
    ),
    retrieve=extend_schema(
        summary="获取用户详情",
        description="根据用户ID获取用户的详细信息",
        tags=["用户认证"]
    ),
    create=extend_schema(
        summary="创建用户",
        description="创建新用户账户（管理员功能）",
        tags=["用户认证"]
    ),
    update=extend_schema(
        summary="更新用户信息",
        description="更新用户的基本信息",
        tags=["用户认证"]
    ),
    partial_update=extend_schema(
        summary="部分更新用户信息",
        description="部分更新用户的基本信息",
        tags=["用户认证"]
    ),
    destroy=extend_schema(
        summary="删除用户",
        description="删除用户账户（管理员功能）",
        tags=["用户认证"]
    ),
)
class UserViewSet(viewsets.ModelViewSet):
    """
    用户视图集 - 薄层控制器
    
    职责：
    1. 处理用户相关的HTTP请求
    2. 调用用户业务服务
    3. 返回标准化响应
    4. 处理权限控制
    """
    
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_service = UserService()
    
    @extend_schema(
        summary="用户注册",
        description="注册新用户账户，需要提供用户名、邮箱、密码等信息",
        tags=["用户认证"],
        request=UserRegistrationSerializer,
        responses={
            201: OpenApiExample(
                "注册成功",
                value={
                    "success": True,
                    "message": "用户注册成功",
                    "data": {
                        "user_id": "123",
                        "username": "testuser",
                        "email": "<EMAIL>",
                        "status": "pending_verification",
                        "message": "注册成功，请查收验证邮件"
                    }
                }
            ),
            400: OpenApiExample(
                "注册失败",
                value={
                    "success": False,
                    "message": "注册失败",
                    "errors": {
                        "username": ["用户名已存在"],
                        "email": ["邮箱格式不正确"]
                    }
                }
            )
        }
    )
    @action(detail=False, methods=['post', 'get'], permission_classes=[AllowAny])
    def register_test(self, request: Request) -> Response:
        """
        测试注册接口 - 返回硬编码数据
        """
        from django.http import JsonResponse

        # 返回硬编码数据测试JSON序列化
        response_data = {
            'success': True,
            'message': '测试注册成功',
            'data': {
                'user_id': '123',
                'username': 'testuser',
                'email': '<EMAIL>',
                'status': 'registered'
            }
        }

        return JsonResponse(response_data, status=201)


    
    @extend_schema(
        summary="用户登录",
        description="用户登录验证，支持用户名或邮箱登录，返回JWT访问令牌",
        tags=["用户认证"],
        request=UserLoginSerializer,
        responses={
            200: OpenApiExample(
                "登录成功",
                value={
                    "success": True,
                    "message": "登录成功",
                    "data": {
                        "user": {
                            "id": "123",
                            "username": "testuser",
                            "email": "<EMAIL>",
                            "display_name": "测试用户",
                            "is_verified": True,
                            "status": "active",
                            "user_type": "regular"
                        },
                        "tokens": {
                            "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                            "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
                        },
                        "session_id": "session_123",
                        "expires_at": 1640995200
                    }
                }
            ),
            401: OpenApiExample(
                "登录失败",
                value={
                    "success": False,
                    "message": "用户名或密码错误",
                    "error_code": "INVALID_CREDENTIALS"
                }
            )
        }
    )
    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def login(self, request: Request) -> Response:
        """
        用户登录接口

        POST /api/v1/auth/login/
        """
        logger.info(f"用户登录请求: {request.data.get('identifier')}")
        
        # 数据验证
        serializer = UserLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 添加设备信息
        credentials = serializer.validated_data
        credentials['device_info'] = {
            'type': 'web',
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'ip_address': self._get_client_ip(request),
            'platform': request.META.get('HTTP_SEC_CH_UA_PLATFORM', ''),
            'screen_resolution': request.data.get('screen_resolution', '')
        }
        
        # 调用业务服务
        result = self.user_service.authenticate_user(credentials)
        
        return APIResponse.success(
            data=result,
            message="登录成功"
        )
    
    @action(detail=False, methods=['post'])
    def logout(self, request: Request) -> Response:
        """
        用户登出接口
        
        POST /api/v1/users/logout/
        """
        logger.info(f"用户登出请求: {request.user.username}")
        
        # 撤销当前会话
        session_id = request.data.get('session_id')
        if session_id:
            self.user_service.revoke_user_session(
                str(request.user.uuid),
                session_id
            )
        
        return APIResponse.success(message="登出成功")
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def me(self, request: Request) -> Response:
        """
        获取当前用户信息
        
        GET /api/v1/users/me/
        """
        # 调用业务服务
        result = self.user_service.get_user_by_id(str(request.user.uuid))
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['patch'])
    def update_profile(self, request: Request) -> Response:
        """
        更新用户资料
        
        PATCH /api/v1/users/update_profile/
        """
        logger.info(f"更新用户资料: {request.user.username}")
        
        # 数据验证
        serializer = UserProfileSerializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.user_service.update_user_profile(
            str(request.user.uuid),
            serializer.validated_data
        )
        
        return APIResponse.updated(
            data=result,
            message=SuccessMessages.USER_UPDATED
        )
    
    @action(detail=False, methods=['patch'])
    def update_avatar(self, request: Request) -> Response:
        """
        更新用户虚拟形象
        
        PATCH /api/v1/users/update_avatar/
        """
        logger.info(f"更新用户形象: {request.user.username}")
        
        # 数据验证
        serializer = UserAvatarSerializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.user_service.update_user_avatar(
            str(request.user.uuid),
            serializer.validated_data
        )
        
        return APIResponse.updated(
            data=result,
            message="虚拟形象更新成功"
        )
    
    @action(detail=False, methods=['get'])
    def sessions(self, request: Request) -> Response:
        """
        获取用户会话列表
        
        GET /api/v1/users/sessions/
        """
        # 调用业务服务
        result = self.user_service.get_user_sessions(str(request.user.uuid))
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['delete'])
    def revoke_session(self, request: Request) -> Response:
        """
        撤销用户会话
        
        DELETE /api/v1/users/revoke_session/
        """
        session_id = request.data.get('session_id')
        if not session_id:
            return APIResponse.validation_error(
                errors={'session_id': ['会话ID不能为空']},
                message="参数验证失败"
            )
        
        # 调用业务服务
        success = self.user_service.revoke_user_session(
            str(request.user.uuid),
            session_id
        )
        
        if success:
            return APIResponse.success(message="会话已撤销")
        else:
            return APIResponse.not_found(message="会话不存在")
    
    @action(detail=False, methods=['get'], permission_classes=[AllowAny])
    @method_decorator(vary_on_headers('Authorization'))
    @method_decorator(cache_page(60 * 2))  # 缓存2分钟
    def search(self, request: Request) -> Response:
        """
        搜索用户
        
        GET /api/v1/users/search/?q=keyword&limit=20
        """
        query = request.query_params.get('q', '').strip()
        if not query:
            return APIResponse.validation_error(
                errors={'q': ['搜索关键词不能为空']},
                message="参数验证失败"
            )
        
        limit = min(int(request.query_params.get('limit', 20)), 50)
        
        # 调用业务服务
        result = self.user_service.search_users(query, limit)
        
        return APIResponse.success(
            data=result,
            meta={'query': query, 'limit': limit, 'count': len(result)}
        )
    
    @action(detail=True, methods=['get'], permission_classes=[AllowAny])
    @method_decorator(cache_page(60 * 10))  # 缓存10分钟
    def profile(self, request: Request, pk=None) -> Response:
        """
        获取用户公开资料
        
        GET /api/v1/users/{id}/profile/
        """
        # 调用业务服务
        result = self.user_service.get_user_by_id(pk)
        
        # 过滤敏感信息
        if str(request.user.uuid) != pk:
            # 非本人访问，只返回公开信息
            result['user'] = self._filter_public_user_data(result['user'])
            result['profile'] = self._filter_public_profile_data(result['profile'])
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['post'])
    def change_password(self, request: Request) -> Response:
        """
        修改密码
        
        POST /api/v1/users/change_password/
        """
        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')
        confirm_password = request.data.get('confirm_password')
        
        # 基础验证
        if not all([current_password, new_password, confirm_password]):
            return APIResponse.validation_error(
                errors={'password': ['所有密码字段都不能为空']},
                message="参数验证失败"
            )
        
        if new_password != confirm_password:
            return APIResponse.validation_error(
                errors={'confirm_password': ['两次输入的密码不一致']},
                message="参数验证失败"
            )
        
        # 验证当前密码
        if not request.user.check_password(current_password):
            return APIResponse.error(
                code=ErrorCodes.INVALID_CREDENTIALS,
                message="当前密码错误",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # 更新密码
        request.user.set_password(new_password)
        request.user.save()
        
        logger.info(f"用户密码修改成功: {request.user.username}")
        
        return APIResponse.success(message=SuccessMessages.PASSWORD_CHANGED)
    
    @action(detail=False, methods=['post'])
    def send_verification_email(self, request: Request) -> Response:
        """
        发送邮箱验证邮件
        
        POST /api/v1/users/send_verification_email/
        """
        if request.user.is_verified:
            return APIResponse.error(
                code="ALREADY_VERIFIED",
                message="邮箱已经验证过了",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # 异步发送验证邮件
        from .tasks import send_verification_email
        send_verification_email.delay(request.user.id)
        
        return APIResponse.success(message="验证邮件已发送")
    
    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def verify_email(self, request: Request) -> Response:
        """
        验证邮箱
        
        POST /api/v1/users/verify_email/
        """
        verification_code = request.data.get('code')
        if not verification_code:
            return APIResponse.validation_error(
                errors={'code': ['验证码不能为空']},
                message="参数验证失败"
            )
        
        # TODO: 实现邮箱验证逻辑
        # 这里需要实现验证码验证逻辑
        
        return APIResponse.success(message=SuccessMessages.EMAIL_VERIFIED)
    
    @action(detail=False, methods=['get'])
    def stats(self, request: Request) -> Response:
        """
        获取用户统计信息
        
        GET /api/v1/users/stats/
        """
        result = self.user_service.get_user_by_id(str(request.user.uuid))
        
        return APIResponse.success(data=result['stats'])
    
    # 私有方法
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _filter_public_user_data(self, user_data: dict) -> dict:
        """过滤用户公开数据"""
        public_fields = [
            'id', 'username', 'is_active', 'is_verified', 
            'status', 'user_type', 'created_at'
        ]
        return {k: v for k, v in user_data.items() if k in public_fields}
    
    def _filter_public_profile_data(self, profile_data: dict) -> dict:
        """过滤资料公开数据"""
        if not profile_data:
            return None
        
        public_fields = [
            'id', 'nickname', 'bio', 'avatar_url', 
            'website', 'social_links', 'created_at'
        ]
        return {k: v for k, v in profile_data.items() if k in public_fields}


class UserProfileViewSet(viewsets.ModelViewSet):
    """用户资料视图集"""
    
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated, IsProfileOwner]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_service = UserService()
    def update(self, request: Request, *args, **kwargs) -> Response:
        """更新用户资料"""
        # 调用业务服务
        result = self.user_service.update_user_profile(
            str(request.user.uuid),
            request.data
        )
        
        return APIResponse.updated(
            data=result,
            message=SuccessMessages.USER_UPDATED
        )
    def partial_update(self, request: Request, *args, **kwargs) -> Response:
        """部分更新用户资料"""
        return self.update(request, *args, **kwargs)


class UserAvatarViewSet(viewsets.ModelViewSet):
    """用户虚拟形象视图集"""
    
    serializer_class = UserAvatarSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReadOnly]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_service = UserService()
    def update(self, request: Request, *args, **kwargs) -> Response:
        """更新虚拟形象"""
        # 调用业务服务
        result = self.user_service.update_user_avatar(
            str(request.user.uuid),
            request.data
        )
        
        return APIResponse.updated(
            data=result,
            message="虚拟形象更新成功"
        )
    def partial_update(self, request: Request, *args, **kwargs) -> Response:
        """部分更新虚拟形象"""
        return self.update(request, *args, **kwargs)


class UserSessionViewSet(viewsets.ReadOnlyModelViewSet):
    """用户会话视图集"""
    
    serializer_class = UserSessionSerializer
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_service = UserService()
    def list(self, request: Request) -> Response:
        """获取会话列表"""
        result = self.user_service.get_user_sessions(str(request.user.uuid))
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['delete'])
    def revoke(self, request: Request, pk=None) -> Response:
        """撤销会话"""
        success = self.user_service.revoke_user_session(
            str(request.user.uuid),
            pk
        )
        
        if success:
            return APIResponse.success(message="会话已撤销")
        else:
            return APIResponse.not_found(message="会话不存在")


class RegisterView(APIView):
    """
    用户注册接口 - 独立的APIView实现

    POST /api/v1/auth/register/
    """
    permission_classes = [AllowAny]

    @extend_schema(
        operation_id='user_register',
        summary='用户注册',
        description='注册新用户账户，需要提供用户名、邮箱、密码等信息',
        tags=['用户认证'],
        request='UserRegistrationSerializer',
        responses={
            201: OpenApiResponse(
                description='注册成功',
                examples=[
                    OpenApiExample(
                        'Success',
                        summary='注册成功示例',
                        description='用户注册成功的响应',
                        value={
                            "success": True,
                            "message": "用户注册成功",
                            "data": {
                                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                                "username": "newuser",
                                "email": "<EMAIL>",
                                "nickname": "新用户",
                                "status": "pending_verification",
                                "message": "注册成功，请查收验证邮件",
                                "created_at": "2025-08-05T14:30:00+08:00",
                                "is_verified": False
                            }
                        }
                    )
                ]
            ),
            400: OpenApiResponse(
                description='请求参数错误',
                examples=[
                    OpenApiExample(
                        'Validation Error',
                        summary='验证错误示例',
                        description='用户名或邮箱已存在',
                        value={
                            "success": False,
                            "message": "注册失败: 用户名已存在",
                            "error": "用户名已存在"
                        }
                    )
                ]
            ),
            500: OpenApiResponse(
                description='服务器内部错误',
                examples=[
                    OpenApiExample(
                        'Server Error',
                        summary='服务器错误示例',
                        description='服务器内部错误',
                        value={
                            "success": False,
                            "message": "注册失败: 服务器内部错误",
                            "error": "Internal server error"
                        }
                    )
                ]
            )
        }
    )
    def post(self, request):
        """
        用户注册

        注册新用户账户，需要提供用户名、邮箱、密码等信息

        Args:
            request: HTTP请求对象，包含用户注册数据

        Returns:
            HttpResponse: JSON格式的注册结果

        Raises:
            ValidationError: 当输入数据验证失败时
            Exception: 当注册过程中发生其他错误时
        """
        from django.http import HttpResponse
        from .serializers import UserRegistrationSerializer
        from apps.core.dependencies import get_service
        import json
        import logging

        logger = logging.getLogger(__name__)

        try:
            # 获取用户服务
            user_service = get_service('UserService')

            # 数据验证
            serializer = UserRegistrationSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # 调用业务服务进行用户注册
            result = user_service.register_user(serializer.validated_data)

            # 构建标准化响应数据
            response_data = {
                'success': True,
                'message': '用户注册成功',
                'data': {
                    'user_id': str(result.get('user_id', '')),
                    'username': str(result.get('username', '')),
                    'email': str(result.get('email', '')),
                    'nickname': str(result.get('nickname', '')),
                    'status': str(result.get('status', 'registered')),
                    'message': str(result.get('message', '')),
                    'created_at': str(result.get('created_at', '')),
                    'is_verified': bool(result.get('is_verified', False))
                }
            }

            # 返回JSON响应
            return HttpResponse(
                json.dumps(response_data),
                content_type='application/json',
                status=201
            )

        except Exception as e:
            logger.error(f"用户注册失败: {str(e)}")

            # 构建错误响应
            error_data = {
                'success': False,
                'message': f'注册失败: {str(e)}',
                'error': str(e)
            }

            return HttpResponse(
                json.dumps(error_data),
                content_type='application/json',
                status=400 if 'already exists' in str(e) or 'already registered' in str(e) else 500
            )


class RegisterTestView(APIView):
    """
    测试注册接口 - 返回硬编码数据
    """
    permission_classes = [AllowAny]

    def post(self, request):
        from django.http import JsonResponse

        # 返回硬编码数据测试JSON序列化
        response_data = {
            'success': True,
            'message': '测试注册成功',
            'data': {
                'user_id': '123',
                'username': 'testuser',
                'email': '<EMAIL>',
                'status': 'registered'
            }
        }

        return JsonResponse(response_data, status=201)

    def get(self, request):
        return self.post(request)


@csrf_exempt
@require_http_methods(["POST"])
def register_user_standalone(request):
    """
    独立的用户注册视图函数 - 完全绕过DRF
    """
    try:
        # 解析JSON数据
        data = json.loads(request.body)

        # 简单验证
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')

        if not all([username, email, password]):
            return JsonResponse({
                'success': False,
                'message': '缺少必需字段',
                'error': 'Missing required fields'
            }, status=400)

        # 检查用户是否已存在
        from .models import User
        if User.objects.filter(username=username).exists():
            return JsonResponse({
                'success': False,
                'message': '用户名已存在',
                'error': 'Username already exists'
            }, status=400)

        if User.objects.filter(email=email).exists():
            return JsonResponse({
                'success': False,
                'message': '邮箱已被注册',
                'error': 'Email already registered'
            }, status=400)

        # 创建用户
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password
        )

        # 返回成功响应
        response_data = {
            'success': True,
            'message': '用户注册成功',
            'data': {
                'user_id': str(user.id),
                'username': user.username,
                'email': user.email,
                'status': 'registered'
            }
        }

        return JsonResponse(response_data, status=201)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': '无效的JSON数据',
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'注册失败: {str(e)}',
            'error': str(e)
        }, status=500)