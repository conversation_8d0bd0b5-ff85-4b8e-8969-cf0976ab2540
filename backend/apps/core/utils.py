"""
核心工具函数
包含API文档生成和其他通用功能
"""

import re
from django.utils.text import camel_case_to_spaces


def custom_operation_id_generator(path, method):
    """
    自定义操作ID生成器
    生成更友好的中文操作ID
    """
    # 移除API版本前缀
    clean_path = path.replace('/api/v1/', '')
    
    # 分割路径
    path_parts = [part for part in clean_path.split('/') if part and not part.startswith('{')]
    
    # HTTP方法映射
    method_mapping = {
        'GET': '获取',
        'POST': '创建', 
        'PUT': '更新',
        'PATCH': '修改',
        'DELETE': '删除'
    }
    
    # 路径映射到中文
    path_mapping = {
        'auth': '认证',
        'users': '用户',
        'profiles': '资料',
        'avatars': '头像',
        'sessions': '会话',
        'social': '社交',
        'friendships': '好友关系',
        'follows': '关注',
        'groups': '群组',
        'recommendations': '推荐',
        'messaging': '消息',
        'conversations': '对话',
        'messages': '消息',
        'online-status': '在线状态',
        'content': '内容',
        'posts': '帖子',
        'comments': '评论',
        'categories': '分类',
        'economy': '经济',
        'wallets': '钱包',
        'products': '商品',
        'orders': '订单',
        'transactions': '交易',
        'core': '核心',
        'health': '健康检查',
        'stats': '统计',
        'info': '信息'
    }
    
    # 构建操作ID
    method_name = method_mapping.get(method.upper(), method.upper())
    
    if path_parts:
        # 转换路径部分为中文
        chinese_parts = []
        for part in path_parts:
            # 处理连字符分隔的词
            sub_parts = part.split('-')
            chinese_sub_parts = []
            
            for sub_part in sub_parts:
                chinese_part = path_mapping.get(sub_part.lower(), sub_part)
                chinese_sub_parts.append(chinese_part)
            
            chinese_parts.append(''.join(chinese_sub_parts))
        
        resource_name = chinese_parts[-1]  # 使用最后一个部分作为资源名
        operation_id = f"{method_name}{resource_name}"
    else:
        operation_id = f"{method_name}根路径"
    
    # 清理操作ID，确保符合OpenAPI规范
    operation_id = re.sub(r'[^\w\u4e00-\u9fff]', '', operation_id)
    
    return operation_id


def get_api_tag_for_path(path):
    """
    根据路径获取对应的API标签
    """
    tag_mapping = {
        '/api/v1/auth/': '用户认证',
        '/api/v1/social/': '社交功能',
        '/api/v1/messaging/': '消息系统',
        '/api/v1/content/': '内容管理',
        '/api/v1/economy/': '经济系统',
        '/api/v1/core/': '系统核心',
    }
    
    for path_prefix, tag_name in tag_mapping.items():
        if path.startswith(path_prefix):
            return tag_name
    
    return '其他'


def format_api_description(description, path=None, method=None):
    """
    格式化API描述，添加路径和方法信息
    """
    if not description:
        return description
    
    formatted_desc = description
    
    # 添加路径信息
    if path:
        formatted_desc += f"\n\n**API路径**: `{path}`"
    
    # 添加HTTP方法信息
    if method:
        method_descriptions = {
            'GET': '获取数据，不会修改服务器状态',
            'POST': '创建新资源或提交数据',
            'PUT': '完整更新现有资源',
            'PATCH': '部分更新现有资源',
            'DELETE': '删除指定资源'
        }
        
        method_desc = method_descriptions.get(method.upper())
        if method_desc:
            formatted_desc += f"\n\n**HTTP方法**: {method.upper()} - {method_desc}"
    
    return formatted_desc


def get_chinese_model_name(model_name):
    """
    将英文模型名转换为中文名称
    """
    model_mapping = {
        'User': '用户',
        'UserProfile': '用户资料',
        'UserAvatar': '用户头像',
        'UserSession': '用户会话',
        'Friendship': '好友关系',
        'Follow': '关注关系',
        'Group': '群组',
        'Recommendation': '推荐',
        'Conversation': '对话',
        'Message': '消息',
        'OnlineStatus': '在线状态',
        'Post': '帖子',
        'Comment': '评论',
        'ContentCategory': '内容分类',
        'Wallet': '钱包',
        'Product': '商品',
        'Order': '订单',
        'Transaction': '交易',
    }
    
    return model_mapping.get(model_name, model_name)


def get_api_examples():
    """
    获取API示例数据
    """
    return {
        'user_login': {
            'summary': '用户登录示例',
            'value': {
                'username': 'testuser',
                'password': 'password123'
            }
        },
        'user_register': {
            'summary': '用户注册示例',
            'value': {
                'username': 'newuser',
                'email': '<EMAIL>',
                'password': 'password123',
                'confirm_password': 'password123'
            }
        },
        'post_create': {
            'summary': '创建帖子示例',
            'value': {
                'title': '我的第一篇帖子',
                'content': '这是帖子的内容...',
                'category': 1,
                'tags': ['技术', '分享']
            }
        },
        'message_send': {
            'summary': '发送消息示例',
            'value': {
                'recipient': 2,
                'content': '你好，这是一条测试消息',
                'message_type': 'text'
            }
        }
    }


class APIDocumentationMixin:
    """
    API文档混入类
    为ViewSet提供统一的中文文档配置
    """
    
    def get_operation_description(self, action):
        """获取操作描述"""
        descriptions = {
            'list': f'获取{self.get_model_verbose_name()}列表',
            'create': f'创建新的{self.get_model_verbose_name()}',
            'retrieve': f'获取指定{self.get_model_verbose_name()}详情',
            'update': f'完整更新{self.get_model_verbose_name()}',
            'partial_update': f'部分更新{self.get_model_verbose_name()}',
            'destroy': f'删除{self.get_model_verbose_name()}',
        }
        return descriptions.get(action, f'{action}操作')
    
    def get_model_verbose_name(self):
        """获取模型的中文名称"""
        if hasattr(self, 'queryset') and self.queryset is not None:
            model = self.queryset.model
            return getattr(model._meta, 'verbose_name', model.__name__)
        return '资源'
