"""
API示例数据配置
为DRF Spectacular提供中文示例数据
"""

from drf_spectacular.utils import OpenApiExample


# 用户认证相关示例
USER_LOGIN_EXAMPLES = [
    OpenApiExample(
        name='用户登录示例',
        summary='标准用户登录',
        description='使用用户名和密码进行登录',
        value={
            'username': 'testuser',
            'password': 'password123'
        },
        request_only=True,
    ),
    OpenApiExample(
        name='邮箱登录示例',
        summary='使用邮箱登录',
        description='使用邮箱地址和密码进行登录',
        value={
            'email': '<EMAIL>',
            'password': 'password123'
        },
        request_only=True,
    ),
]

USER_REGISTER_EXAMPLES = [
    OpenApiExample(
        name='用户注册示例',
        summary='新用户注册',
        description='创建新用户账户',
        value={
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'password123',
            'confirm_password': 'password123',
            'first_name': '张',
            'last_name': '三'
        },
        request_only=True,
    ),
]

USER_PROFILE_EXAMPLES = [
    OpenApiExample(
        name='用户资料示例',
        summary='完整用户资料',
        description='用户的详细资料信息',
        value={
            'bio': '这是我的个人简介',
            'location': '北京市',
            'website': 'https://example.com',
            'birth_date': '1990-01-01',
            'gender': 'male',
            'phone': '13800138000'
        },
    ),
]

# 社交功能相关示例
FRIEND_REQUEST_EXAMPLES = [
    OpenApiExample(
        name='好友请求示例',
        summary='发送好友请求',
        description='向其他用户发送好友请求',
        value={
            'target_user_id': 'uuid-string-here',
            'message': '你好，我想和你成为好友！'
        },
        request_only=True,
    ),
]

GROUP_CREATE_EXAMPLES = [
    OpenApiExample(
        name='创建群组示例',
        summary='创建新群组',
        description='创建一个新的社交群组',
        value={
            'name': '技术交流群',
            'description': '讨论技术问题和分享经验的群组',
            'is_public': True,
            'max_members': 100,
            'tags': ['技术', '编程', '交流']
        },
        request_only=True,
    ),
]

# 消息系统相关示例
MESSAGE_SEND_EXAMPLES = [
    OpenApiExample(
        name='发送文本消息',
        summary='发送普通文本消息',
        description='在会话中发送文本消息',
        value={
            'conversation_id': 'uuid-string-here',
            'content': '你好，这是一条测试消息',
            'message_type': 'text'
        },
        request_only=True,
    ),
    OpenApiExample(
        name='发送图片消息',
        summary='发送图片消息',
        description='在会话中发送图片消息',
        value={
            'conversation_id': 'uuid-string-here',
            'content': '看看这张图片！',
            'message_type': 'image',
            'media_url': 'https://example.com/image.jpg'
        },
        request_only=True,
    ),
]

CONVERSATION_CREATE_EXAMPLES = [
    OpenApiExample(
        name='创建私聊会话',
        summary='创建一对一私聊',
        description='与另一个用户创建私聊会话',
        value={
            'type': 'private',
            'participants': ['uuid-user-1', 'uuid-user-2']
        },
        request_only=True,
    ),
    OpenApiExample(
        name='创建群聊会话',
        summary='创建群聊会话',
        description='创建多人群聊会话',
        value={
            'type': 'group',
            'name': '项目讨论组',
            'participants': ['uuid-user-1', 'uuid-user-2', 'uuid-user-3']
        },
        request_only=True,
    ),
]

# 内容管理相关示例
POST_CREATE_EXAMPLES = [
    OpenApiExample(
        name='创建文本帖子',
        summary='发布文本帖子',
        description='创建一个纯文本的帖子',
        value={
            'title': '我的第一篇帖子',
            'content': '这是帖子的内容，可以包含丰富的文本信息...',
            'category_id': 1,
            'tags': ['生活', '分享'],
            'is_public': True
        },
        request_only=True,
    ),
    OpenApiExample(
        name='创建图文帖子',
        summary='发布图文帖子',
        description='创建包含图片的帖子',
        value={
            'title': '美丽的风景',
            'content': '今天拍到了很美的风景，分享给大家！',
            'category_id': 2,
            'tags': ['摄影', '风景'],
            'images': ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
            'is_public': True
        },
        request_only=True,
    ),
]

COMMENT_CREATE_EXAMPLES = [
    OpenApiExample(
        name='发表评论示例',
        summary='对帖子发表评论',
        description='在帖子下方发表评论',
        value={
            'post_id': 'uuid-string-here',
            'content': '很棒的分享，谢谢！',
            'parent_comment_id': None  # 如果是回复评论，则填写父评论ID
        },
        request_only=True,
    ),
]

# 经济系统相关示例
PRODUCT_CREATE_EXAMPLES = [
    OpenApiExample(
        name='创建商品示例',
        summary='发布新商品',
        description='在平台上发布一个新商品',
        value={
            'name': '精美手工艺品',
            'description': '纯手工制作的精美艺术品，独一无二',
            'price': 99.99,
            'currency': 'CNY',
            'category': '手工艺品',
            'images': ['https://example.com/product1.jpg'],
            'stock_quantity': 10,
            'is_active': True
        },
        request_only=True,
    ),
]

ORDER_CREATE_EXAMPLES = [
    OpenApiExample(
        name='创建订单示例',
        summary='购买商品',
        description='创建商品购买订单',
        value={
            'product_id': 'uuid-string-here',
            'quantity': 1,
            'shipping_address': {
                'name': '张三',
                'phone': '13800138000',
                'address': '北京市朝阳区某某街道123号',
                'postal_code': '100000'
            },
            'payment_method': 'alipay'
        },
        request_only=True,
    ),
]

# 响应示例
SUCCESS_RESPONSE_EXAMPLES = [
    OpenApiExample(
        name='成功响应示例',
        summary='标准成功响应',
        description='API调用成功时的标准响应格式',
        value={
            'success': True,
            'message': '操作成功',
            'data': {
                'id': 'uuid-string-here',
                'created_at': '2025-01-01T00:00:00Z'
            },
            'timestamp': '2025-01-01T00:00:00Z'
        },
        response_only=True,
    ),
]

ERROR_RESPONSE_EXAMPLES = [
    OpenApiExample(
        name='错误响应示例',
        summary='标准错误响应',
        description='API调用失败时的标准响应格式',
        value={
            'success': False,
            'message': '参数验证失败',
            'errors': {
                'username': ['用户名已存在'],
                'email': ['邮箱格式不正确']
            },
            'error_code': 'VALIDATION_ERROR',
            'timestamp': '2025-01-01T00:00:00Z'
        },
        response_only=True,
    ),
]

# 分页响应示例
PAGINATED_RESPONSE_EXAMPLES = [
    OpenApiExample(
        name='分页响应示例',
        summary='分页数据响应',
        description='带分页信息的数据响应格式',
        value={
            'success': True,
            'data': {
                'results': [
                    {'id': 1, 'name': '示例数据1'},
                    {'id': 2, 'name': '示例数据2'}
                ],
                'pagination': {
                    'count': 100,
                    'page': 1,
                    'page_size': 20,
                    'total_pages': 5,
                    'has_next': True,
                    'has_previous': False
                }
            },
            'timestamp': '2025-01-01T00:00:00Z'
        },
        response_only=True,
    ),
]
