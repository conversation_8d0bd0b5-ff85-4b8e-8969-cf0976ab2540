"""
DRF Spectacular 自定义钩子
用于处理API文档的标签和分类
"""

from django.conf import settings


def postprocess_schema_tags(result, generator, request, public):
    """
    后处理钩子：为API端点自动分配中文标签
    根据URL路径自动将端点分配到对应的业务域标签
    """
    if not hasattr(settings, 'SPECTACULAR_SETTINGS'):
        return result
    
    # 获取标签映射配置
    tags_mapping = getattr(settings, 'SPECTACULAR_SETTINGS', {}).get('TAGS_PATH_MAPPING', {})
    
    if not tags_mapping:
        return result
    
    # 处理每个路径的标签
    for path, path_info in result.get('paths', {}).items():
        # 确定该路径应该属于哪个标签
        assigned_tag = None
        
        for path_prefix, tag_name in tags_mapping.items():
            if path.startswith(path_prefix):
                assigned_tag = tag_name
                break
        
        if assigned_tag:
            # 为该路径下的所有HTTP方法分配标签
            for method, operation in path_info.items():
                if method.lower() in ['get', 'post', 'put', 'patch', 'delete', 'head', 'options']:
                    if 'tags' not in operation:
                        operation['tags'] = []
                    
                    # 如果还没有这个标签，就添加
                    if assigned_tag not in operation['tags']:
                        operation['tags'].append(assigned_tag)
                    
                    # 确保标签在最前面（主要分类）
                    if len(operation['tags']) > 1 and operation['tags'][0] != assigned_tag:
                        operation['tags'].remove(assigned_tag)
                        operation['tags'].insert(0, assigned_tag)
    
    # 确保所有定义的标签都在schema中
    if 'tags' not in result:
        result['tags'] = []
    
    # 获取已定义的标签
    defined_tags = getattr(settings, 'SPECTACULAR_SETTINGS', {}).get('TAGS', [])
    existing_tag_names = {tag.get('name') for tag in result['tags']}
    
    # 添加缺失的标签定义
    for tag_def in defined_tags:
        if tag_def.get('name') not in existing_tag_names:
            result['tags'].append(tag_def)
    
    return result


def preprocess_exclude_unused_tags(endpoints):
    """
    预处理钩子：排除未使用的标签
    """
    return endpoints


def postprocess_operation_summary(result, generator, request, public):
    """
    后处理钩子：优化操作摘要的中文显示
    """
    for path, path_info in result.get('paths', {}).items():
        for method, operation in path_info.items():
            if method.lower() in ['get', 'post', 'put', 'patch', 'delete']:
                # 优化摘要显示
                if 'summary' in operation:
                    summary = operation['summary']
                    
                    # 添加中文HTTP方法说明
                    method_names = {
                        'get': '获取',
                        'post': '创建',
                        'put': '更新',
                        'patch': '部分更新',
                        'delete': '删除'
                    }
                    
                    method_name = method_names.get(method.lower(), method.upper())
                    
                    # 如果摘要中没有包含方法说明，则添加
                    if not any(name in summary for name in method_names.values()):
                        operation['summary'] = f"{method_name}{summary}"
                
                # 优化描述显示
                if 'description' in operation and operation['description']:
                    description = operation['description']
                    
                    # 添加路径信息到描述中
                    if '**路径**' not in description:
                        operation['description'] = f"{description}\n\n**路径**: `{path}`"
    
    return result
