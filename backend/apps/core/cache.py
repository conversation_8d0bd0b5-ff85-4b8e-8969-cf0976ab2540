"""
SOIC 缓存管理工具
提供智能缓存策略和缓存管理功能
"""

import hashlib
import json
import time
from functools import wraps
from typing import Any, Optional, Union, Callable
from django.core.cache import cache, caches
from django.conf import settings
from django.utils.encoding import force_str
from django.http import HttpRequest


class CacheManager:
    """缓存管理器"""
    
    # 缓存时间常量
    CACHE_TIMEOUT_SHORT = 300      # 5分钟
    CACHE_TIMEOUT_MEDIUM = 1800    # 30分钟
    CACHE_TIMEOUT_LONG = 3600      # 1小时
    CACHE_TIMEOUT_VERY_LONG = 86400  # 24小时
    
    # 缓存键前缀
    PREFIX_USER = 'user'
    PREFIX_API = 'api'
    PREFIX_SOCIAL = 'social'
    PREFIX_CONTENT = 'content'
    PREFIX_MESSAGING = 'messaging'
    PREFIX_ECONOMY = 'economy'
    
    def __init__(self, cache_alias: str = 'default'):
        self.cache = caches[cache_alias]
    
    def generate_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [prefix]
        
        # 添加位置参数
        for arg in args:
            if isinstance(arg, (dict, list)):
                key_parts.append(hashlib.md5(json.dumps(arg, sort_keys=True).encode()).hexdigest()[:8])
            else:
                key_parts.append(str(arg))
        
        # 添加关键字参数
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            kwargs_str = json.dumps(sorted_kwargs, sort_keys=True)
            key_parts.append(hashlib.md5(kwargs_str.encode()).hexdigest()[:8])
        
        return ':'.join(key_parts)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        return self.cache.get(key, default)
    
    def set(self, key: str, value: Any, timeout: int = CACHE_TIMEOUT_MEDIUM) -> bool:
        """设置缓存值"""
        return self.cache.set(key, value, timeout)
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        return self.cache.delete(key)
    
    def delete_pattern(self, pattern: str) -> int:
        """删除匹配模式的缓存"""
        if hasattr(self.cache, 'delete_pattern'):
            return self.cache.delete_pattern(pattern)
        return 0
    
    def get_or_set(self, key: str, callable_func: Callable, timeout: int = CACHE_TIMEOUT_MEDIUM) -> Any:
        """获取缓存，如果不存在则设置"""
        value = self.get(key)
        if value is None:
            value = callable_func()
            self.set(key, value, timeout)
        return value
    
    def invalidate_user_cache(self, user_id: Union[int, str]):
        """清除用户相关缓存"""
        patterns = [
            f"{self.PREFIX_USER}:{user_id}:*",
            f"{self.PREFIX_SOCIAL}:{user_id}:*",
            f"{self.PREFIX_MESSAGING}:{user_id}:*",
        ]
        
        for pattern in patterns:
            self.delete_pattern(pattern)
    
    def invalidate_content_cache(self, content_id: Union[int, str]):
        """清除内容相关缓存"""
        patterns = [
            f"{self.PREFIX_CONTENT}:{content_id}:*",
            f"{self.PREFIX_CONTENT}:list:*",  # 清除列表缓存
        ]
        
        for pattern in patterns:
            self.delete_pattern(pattern)


# 全局缓存管理器实例
cache_manager = CacheManager()
api_cache_manager = CacheManager('api_cache')


def cache_result(timeout: int = CacheManager.CACHE_TIMEOUT_MEDIUM, 
                key_prefix: str = 'func',
                cache_alias: str = 'default'):
    """
    函数结果缓存装饰器
    
    Args:
        timeout: 缓存超时时间（秒）
        key_prefix: 缓存键前缀
        cache_alias: 缓存别名
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_mgr = CacheManager(cache_alias)
            cache_key = cache_mgr.generate_key(key_prefix, func.__name__, *args, **kwargs)
            
            # 尝试从缓存获取
            result = cache_mgr.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_mgr.set(cache_key, result, timeout)
            
            return result
        return wrapper
    return decorator


def cache_api_response(timeout: int = CacheManager.CACHE_TIMEOUT_SHORT,
                      vary_on_user: bool = True,
                      vary_on_params: bool = True):
    """
    API响应缓存装饰器
    
    Args:
        timeout: 缓存超时时间
        vary_on_user: 是否根据用户区分缓存
        vary_on_params: 是否根据请求参数区分缓存
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 生成缓存键
            key_parts = ['api', view_func.__name__]
            
            if vary_on_user and hasattr(request, 'user') and request.user.is_authenticated:
                key_parts.append(f"user_{request.user.id}")
            
            if vary_on_params:
                # 添加URL参数
                if args:
                    key_parts.extend(str(arg) for arg in args)
                if kwargs:
                    key_parts.append(hashlib.md5(json.dumps(kwargs, sort_keys=True).encode()).hexdigest()[:8])
                
                # 添加查询参数
                if request.GET:
                    query_hash = hashlib.md5(request.GET.urlencode().encode()).hexdigest()[:8]
                    key_parts.append(f"query_{query_hash}")
            
            cache_key = ':'.join(key_parts)
            
            # 尝试从缓存获取
            cached_response = api_cache_manager.get(cache_key)
            if cached_response is not None:
                return cached_response
            
            # 执行视图函数
            response = view_func(request, *args, **kwargs)
            
            # 只缓存成功的响应
            if hasattr(response, 'status_code') and response.status_code == 200:
                api_cache_manager.set(cache_key, response, timeout)
            
            return response
        return wrapper
    return decorator


class SmartCacheMiddleware:
    """智能缓存中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 检查是否应该缓存
        if not self.should_cache_request(request):
            return self.get_response(request)
        
        # 生成缓存键
        cache_key = self.generate_request_key(request)
        
        # 尝试从缓存获取响应
        cached_response = cache.get(cache_key)
        if cached_response is not None:
            return cached_response
        
        # 获取响应
        response = self.get_response(request)
        
        # 缓存响应
        if self.should_cache_response(response):
            timeout = self.get_cache_timeout(request)
            cache.set(cache_key, response, timeout)
        
        return response
    
    def should_cache_request(self, request: HttpRequest) -> bool:
        """判断是否应该缓存请求"""
        # 只缓存GET请求
        if request.method != 'GET':
            return False
        
        # 不缓存认证用户的个人数据
        if request.user.is_authenticated and 'profile' in request.path:
            return False
        
        # 不缓存管理后台
        if request.path.startswith('/admin/'):
            return False
        
        return True
    
    def should_cache_response(self, response) -> bool:
        """判断是否应该缓存响应"""
        return hasattr(response, 'status_code') and response.status_code == 200
    
    def generate_request_key(self, request: HttpRequest) -> str:
        """生成请求缓存键"""
        key_parts = ['request', request.path]
        
        if request.GET:
            query_hash = hashlib.md5(request.GET.urlencode().encode()).hexdigest()[:8]
            key_parts.append(query_hash)
        
        if request.user.is_authenticated:
            key_parts.append(f"user_{request.user.id}")
        
        return ':'.join(key_parts)
    
    def get_cache_timeout(self, request: HttpRequest) -> int:
        """获取缓存超时时间"""
        # API端点使用较短的缓存时间
        if request.path.startswith('/api/'):
            return CacheManager.CACHE_TIMEOUT_SHORT
        
        # 静态内容使用较长的缓存时间
        if any(request.path.startswith(prefix) for prefix in ['/static/', '/media/']):
            return CacheManager.CACHE_TIMEOUT_VERY_LONG
        
        return CacheManager.CACHE_TIMEOUT_MEDIUM


# 缓存工具函数
def warm_up_cache():
    """预热缓存"""
    from django.contrib.auth import get_user_model
    
    User = get_user_model()
    
    # 预热用户统计数据
    user_count = User.objects.count()
    cache_manager.set('stats:user_count', user_count, CacheManager.CACHE_TIMEOUT_LONG)
    
    print(f"缓存预热完成，用户数量: {user_count}")


def clear_all_cache():
    """清除所有缓存"""
    cache.clear()
    if hasattr(caches['api_cache'], 'clear'):
        caches['api_cache'].clear()
    
    print("所有缓存已清除")


def get_cache_stats():
    """获取缓存统计信息"""
    stats = {
        'default_cache': 'Redis' if 'redis' in settings.CACHES['default']['BACKEND'].lower() else 'Other',
        'cache_timeout_short': CacheManager.CACHE_TIMEOUT_SHORT,
        'cache_timeout_medium': CacheManager.CACHE_TIMEOUT_MEDIUM,
        'cache_timeout_long': CacheManager.CACHE_TIMEOUT_LONG,
    }
    
    return stats
