"""
SOIC 后端启动管理类
负责系统启动时的各种检查和初始化工作
"""

import os
import sys
import time
from pathlib import Path
from django.conf import settings
from django.core.management import execute_from_command_line
from django.db import connection
from django.core.cache import cache


class StartupManager:
    """
    启动管理器
    负责系统启动前的环境检查、依赖验证和初始化工作
    """
    
    def __init__(self):
        self.startup_time = time.time()
        self.checks_passed = []
        self.checks_failed = []
        self.warnings = []
    
    def print_banner(self):
        """打印启动横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    SOIC 社交创新社区                          ║
║                  Social Innovation Community                 ║
║                                                              ║
║                    🚀 系统启动中...                          ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        print(f"⏰ 启动时间: {time.strftime('%Y年%m月%d日 %H:%M:%S')}")
        print(f"🐍 Python版本: {sys.version.split()[0]}")
        print(f"📁 项目路径: {Path(__file__).parent.parent.parent}")
        print("=" * 66)
    
    def check_python_version(self):
        """检查Python版本"""
        print("🔍 检查Python版本...")
        
        required_version = (3, 9)
        current_version = sys.version_info[:2]
        
        if current_version >= required_version:
            print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
            self.checks_passed.append("Python版本")
            return True
        else:
            print(f"❌ Python版本过低: {sys.version.split()[0]} (需要 >= 3.9)")
            self.checks_failed.append("Python版本")
            return False
    
    def check_environment_variables(self):
        """检查环境变量"""
        print("🔍 检查环境变量...")
        
        required_vars = [
            'DJANGO_SETTINGS_MODULE',
        ]
        
        optional_vars = [
            'SECRET_KEY',
            'DB_NAME',
            'DB_USER',
            'DB_PASSWORD',
            'DB_HOST',
            'DB_PORT',
        ]
        
        missing_required = []
        missing_optional = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_required.append(var)
        
        for var in optional_vars:
            if not os.getenv(var):
                missing_optional.append(var)
        
        if not missing_required:
            print("✅ 必需环境变量检查通过")
            self.checks_passed.append("环境变量")
        else:
            print(f"❌ 缺少必需环境变量: {', '.join(missing_required)}")
            self.checks_failed.append("环境变量")
        
        if missing_optional:
            print(f"⚠️  缺少可选环境变量: {', '.join(missing_optional)} (将使用默认值)")
            self.warnings.append(f"缺少可选环境变量: {', '.join(missing_optional)}")
        
        return len(missing_required) == 0
    
    def check_database_connection(self):
        """检查数据库连接"""
        print("🔍 检查数据库连接...")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result:
                    print("✅ 数据库连接检查通过")
                    self.checks_passed.append("数据库连接")
                    return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {str(e)}")
            self.checks_failed.append("数据库连接")
            return False
    
    def check_cache_system(self):
        """检查缓存系统"""
        print("🔍 检查缓存系统...")
        
        try:
            test_key = 'startup_test'
            test_value = 'ok'
            
            cache.set(test_key, test_value, 10)
            cached_value = cache.get(test_key)
            
            if cached_value == test_value:
                print("✅ 缓存系统检查通过")
                self.checks_passed.append("缓存系统")
                cache.delete(test_key)
                return True
            else:
                print("❌ 缓存系统读写失败")
                self.checks_failed.append("缓存系统")
                return False
                
        except Exception as e:
            print(f"⚠️  缓存系统检查失败: {str(e)} (将使用内存缓存)")
            self.warnings.append(f"缓存系统异常: {str(e)}")
            return True  # 缓存不是必需的，可以降级使用
    
    def check_required_directories(self):
        """检查必需目录"""
        print("🔍 检查必需目录...")
        
        base_dir = Path(__file__).parent.parent.parent
        required_dirs = [
            'static',
            'media',
            'logs',
            'templates',
        ]
        
        missing_dirs = []
        created_dirs = []
        
        for dir_name in required_dirs:
            dir_path = base_dir / dir_name
            if not dir_path.exists():
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(dir_name)
                except Exception as e:
                    missing_dirs.append(f"{dir_name}: {str(e)}")
        
        if not missing_dirs:
            print("✅ 必需目录检查通过")
            if created_dirs:
                print(f"📁 自动创建目录: {', '.join(created_dirs)}")
            self.checks_passed.append("必需目录")
            return True
        else:
            print(f"❌ 无法创建目录: {', '.join(missing_dirs)}")
            self.checks_failed.append("必需目录")
            return False
    
    def check_business_apps(self):
        """检查业务应用"""
        print("🔍 检查业务应用...")
        
        try:
            from django.apps import apps
            
            expected_apps = {
                'users': '用户认证系统',
                'social': '社交功能',
                'messaging': '消息系统',
                'content': '内容管理',
                'economy': '经济系统',
                'core': '系统核心'
            }
            
            missing_apps = []
            loaded_apps = []
            
            for app_label, description in expected_apps.items():
                try:
                    app_config = apps.get_app_config(app_label)
                    loaded_apps.append(f"{app_label}({description})")
                except Exception as e:
                    # 检查应用是否在INSTALLED_APPS中
                    from django.conf import settings
                    installed_apps = [app.split('.')[-1] for app in settings.INSTALLED_APPS if app.startswith('apps.')]
                    if app_label in installed_apps:
                        loaded_apps.append(f"{app_label}({description})")
                    else:
                        missing_apps.append(f"{app_label}({description})")
            
            if not missing_apps:
                print("✅ 业务应用检查通过")
                print(f"📦 已加载应用: {len(loaded_apps)}个")
                self.checks_passed.append("业务应用")
                return True
            else:
                print(f"❌ 缺少业务应用: {', '.join(missing_apps)}")
                self.checks_failed.append("业务应用")
                return False
                
        except Exception as e:
            print(f"❌ 业务应用检查失败: {str(e)}")
            self.checks_failed.append("业务应用")
            return False
    
    def run_all_checks(self):
        """运行所有检查"""
        print("🔍 开始系统启动检查...")
        print()
        
        checks = [
            self.check_python_version,
            self.check_environment_variables,
            self.check_required_directories,
            self.check_database_connection,
            self.check_cache_system,
            self.check_business_apps,
        ]
        
        all_passed = True
        
        for check in checks:
            try:
                result = check()
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"❌ 检查过程中发生错误: {str(e)}")
                all_passed = False
            print()
        
        return all_passed

    def print_summary(self):
        """打印检查摘要"""
        print("=" * 66)
        print("📊 启动检查摘要")
        print("=" * 66)

        if self.checks_passed:
            print(f"✅ 通过检查 ({len(self.checks_passed)}项):")
            for check in self.checks_passed:
                print(f"   • {check}")

        if self.checks_failed:
            print(f"\n❌ 失败检查 ({len(self.checks_failed)}项):")
            for check in self.checks_failed:
                print(f"   • {check}")

        if self.warnings:
            print(f"\n⚠️  警告信息 ({len(self.warnings)}项):")
            for warning in self.warnings:
                print(f"   • {warning}")

        elapsed_time = time.time() - self.startup_time
        print(f"\n⏱️  检查耗时: {elapsed_time:.2f}秒")

        if not self.checks_failed:
            print("\n🎉 所有检查通过，系统准备就绪！")
            return True
        else:
            print(f"\n💥 发现 {len(self.checks_failed)} 个严重问题，请修复后重试")
            return False

    def print_startup_info(self):
        """打印启动信息"""
        print("\n" + "=" * 66)
        print("🌟 SOIC 社交创新社区启动成功！")
        print("=" * 66)
        print("📍 访问地址:")
        print("   • 主页: http://localhost:8000/")
        print("   • API文档: http://localhost:8000/api/docs/")
        print("   • 管理后台: http://localhost:8000/admin/")
        print("   • 健康检查: http://localhost:8000/api/v1/core/health/")
        print("\n💡 提示:")
        print("   • 按 Ctrl+C 停止服务器")
        print("   • 查看日志: tail -f logs/django.log")
        print("   • 开发文档: README.md")
        print("=" * 66)


def run_startup_checks():
    """
    运行启动检查的便捷函数
    可以在manage.py中调用
    """
    manager = StartupManager()
    manager.print_banner()

    # 运行所有检查
    all_passed = manager.run_all_checks()

    # 打印摘要
    success = manager.print_summary()

    if success:
        manager.print_startup_info()

    return success
