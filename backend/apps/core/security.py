"""
SOIC 安全工具
提供安全检查、防护和监控功能
"""

import hashlib
import hmac
import secrets
import time
from datetime import datetime, timedelta
from functools import wraps
from typing import Optional, Dict, Any
from django.conf import settings
from django.core.cache import cache
from django.http import HttpRequest, JsonResponse
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.response import Response


User = get_user_model()


class SecurityManager:
    """安全管理器"""
    
    # 安全常量
    MAX_LOGIN_ATTEMPTS = 5
    LOGIN_LOCKOUT_DURATION = 900  # 15分钟
    MAX_API_REQUESTS_PER_MINUTE = 60
    MAX_API_REQUESTS_PER_HOUR = 1000
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """生成安全令牌"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def hash_password_with_salt(password: str, salt: Optional[str] = None) -> tuple:
        """使用盐值哈希密码"""
        if salt is None:
            salt = secrets.token_hex(16)
        
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次数
        )
        
        return password_hash.hex(), salt
    
    @staticmethod
    def verify_password(password: str, password_hash: str, salt: str) -> bool:
        """验证密码"""
        computed_hash, _ = SecurityManager.hash_password_with_salt(password, salt)
        return hmac.compare_digest(computed_hash, password_hash)
    
    @staticmethod
    def is_safe_url(url: str, allowed_hosts: list = None) -> bool:
        """检查URL是否安全"""
        if not url:
            return False
        
        if allowed_hosts is None:
            allowed_hosts = settings.ALLOWED_HOSTS
        
        # 简单的URL安全检查
        if url.startswith(('http://', 'https://')):
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc in allowed_hosts
        
        # 相对URL被认为是安全的
        return url.startswith('/')
    
    @staticmethod
    def sanitize_input(input_str: str) -> str:
        """清理用户输入"""
        if not isinstance(input_str, str):
            return str(input_str)
        
        # 移除潜在的恶意字符
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
        for char in dangerous_chars:
            input_str = input_str.replace(char, '')
        
        return input_str.strip()
    
    @staticmethod
    def check_password_strength(password: str) -> Dict[str, Any]:
        """检查密码强度"""
        result = {
            'is_strong': False,
            'score': 0,
            'issues': []
        }
        
        if len(password) < 8:
            result['issues'].append('密码长度至少8位')
        else:
            result['score'] += 1
        
        if not any(c.isupper() for c in password):
            result['issues'].append('需要包含大写字母')
        else:
            result['score'] += 1
        
        if not any(c.islower() for c in password):
            result['issues'].append('需要包含小写字母')
        else:
            result['score'] += 1
        
        if not any(c.isdigit() for c in password):
            result['issues'].append('需要包含数字')
        else:
            result['score'] += 1
        
        if not any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password):
            result['issues'].append('需要包含特殊字符')
        else:
            result['score'] += 1
        
        result['is_strong'] = result['score'] >= 4
        return result


class RateLimiter:
    """API限流器"""
    
    def __init__(self, max_requests: int, time_window: int, key_prefix: str = 'rate_limit'):
        self.max_requests = max_requests
        self.time_window = time_window
        self.key_prefix = key_prefix
    
    def is_allowed(self, identifier: str) -> tuple:
        """检查是否允许请求"""
        cache_key = f"{self.key_prefix}:{identifier}"
        current_time = int(time.time())
        window_start = current_time - self.time_window
        
        # 获取当前窗口内的请求记录
        requests = cache.get(cache_key, [])
        
        # 清理过期的请求记录
        requests = [req_time for req_time in requests if req_time > window_start]
        
        # 检查是否超过限制
        if len(requests) >= self.max_requests:
            return False, self.max_requests - len(requests)
        
        # 添加当前请求
        requests.append(current_time)
        cache.set(cache_key, requests, self.time_window)
        
        return True, self.max_requests - len(requests)
    
    def get_reset_time(self, identifier: str) -> int:
        """获取限制重置时间"""
        cache_key = f"{self.key_prefix}:{identifier}"
        requests = cache.get(cache_key, [])
        
        if not requests:
            return 0
        
        return requests[0] + self.time_window


class LoginAttemptTracker:
    """登录尝试跟踪器"""
    
    @staticmethod
    def get_cache_key(identifier: str) -> str:
        """获取缓存键"""
        return f"login_attempts:{identifier}"
    
    @staticmethod
    def record_failed_attempt(identifier: str):
        """记录失败的登录尝试"""
        cache_key = LoginAttemptTracker.get_cache_key(identifier)
        attempts = cache.get(cache_key, 0)
        attempts += 1
        
        # 设置过期时间
        timeout = SecurityManager.LOGIN_LOCKOUT_DURATION
        cache.set(cache_key, attempts, timeout)
        
        return attempts
    
    @staticmethod
    def clear_attempts(identifier: str):
        """清除登录尝试记录"""
        cache_key = LoginAttemptTracker.get_cache_key(identifier)
        cache.delete(cache_key)
    
    @staticmethod
    def is_locked(identifier: str) -> tuple:
        """检查是否被锁定"""
        cache_key = LoginAttemptTracker.get_cache_key(identifier)
        attempts = cache.get(cache_key, 0)
        
        is_locked = attempts >= SecurityManager.MAX_LOGIN_ATTEMPTS
        remaining_time = 0
        
        if is_locked:
            # 计算剩余锁定时间
            remaining_time = cache.ttl(cache_key) if hasattr(cache, 'ttl') else SecurityManager.LOGIN_LOCKOUT_DURATION
        
        return is_locked, remaining_time


def rate_limit(max_requests: int = 60, time_window: int = 60, key_func: callable = None):
    """
    API限流装饰器
    
    Args:
        max_requests: 最大请求数
        time_window: 时间窗口（秒）
        key_func: 生成限流键的函数
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 生成限流键
            if key_func:
                identifier = key_func(request)
            else:
                identifier = get_client_ip(request)
            
            # 检查限流
            limiter = RateLimiter(max_requests, time_window)
            is_allowed, remaining = limiter.is_allowed(identifier)
            
            if not is_allowed:
                reset_time = limiter.get_reset_time(identifier)
                return JsonResponse({
                    'error': '请求过于频繁，请稍后再试',
                    'error_code': 'RATE_LIMIT_EXCEEDED',
                    'reset_time': reset_time
                }, status=429)
            
            # 添加限流头信息
            response = view_func(request, *args, **kwargs)
            
            if hasattr(response, 'headers'):
                response.headers['X-RateLimit-Limit'] = str(max_requests)
                response.headers['X-RateLimit-Remaining'] = str(remaining)
                response.headers['X-RateLimit-Reset'] = str(int(time.time()) + time_window)
            
            return response
        return wrapper
    return decorator


def require_secure_request(view_func):
    """要求安全请求的装饰器"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # 检查HTTPS
        if not request.is_secure() and not settings.DEBUG:
            return JsonResponse({
                'error': '需要使用HTTPS连接',
                'error_code': 'HTTPS_REQUIRED'
            }, status=400)
        
        # 检查User-Agent
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if not user_agent or len(user_agent) < 10:
            return JsonResponse({
                'error': '无效的请求',
                'error_code': 'INVALID_REQUEST'
            }, status=400)
        
        return view_func(request, *args, **kwargs)
    return wrapper


def get_client_ip(request: HttpRequest) -> str:
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '')
    
    return ip


def log_security_event(event_type: str, request: HttpRequest, details: Dict[str, Any] = None):
    """记录安全事件"""
    import logging
    
    security_logger = logging.getLogger('security')
    
    event_data = {
        'event_type': event_type,
        'timestamp': timezone.now().isoformat(),
        'ip_address': get_client_ip(request),
        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        'path': request.path,
        'method': request.method,
    }
    
    if hasattr(request, 'user') and request.user.is_authenticated:
        event_data['user_id'] = request.user.id
        event_data['username'] = request.user.username
    
    if details:
        event_data.update(details)
    
    security_logger.warning(f"安全事件: {event_type}", extra=event_data)


class SecurityMiddleware:
    """安全中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 安全检查
        if not self.security_check(request):
            return JsonResponse({
                'error': '安全检查失败',
                'error_code': 'SECURITY_CHECK_FAILED'
            }, status=403)
        
        response = self.get_response(request)
        
        # 添加安全头
        self.add_security_headers(response)
        
        return response
    
    def security_check(self, request: HttpRequest) -> bool:
        """执行安全检查"""
        # 检查可疑的请求头
        suspicious_headers = [
            'HTTP_X_FORWARDED_HOST',
            'HTTP_X_ORIGINAL_URL',
            'HTTP_X_REWRITE_URL',
        ]
        
        for header in suspicious_headers:
            if header in request.META:
                log_security_event('SUSPICIOUS_HEADER', request, {
                    'header': header,
                    'value': request.META[header]
                })
        
        # 检查请求大小
        content_length = request.META.get('CONTENT_LENGTH')
        if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB
            log_security_event('LARGE_REQUEST', request, {
                'content_length': content_length
            })
            return False
        
        return True
    
    def add_security_headers(self, response):
        """添加安全头"""
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
        }
        
        for header, value in security_headers.items():
            if hasattr(response, 'headers'):
                response.headers[header] = value


# 全局安全管理器实例
security_manager = SecurityManager()
login_tracker = LoginAttemptTracker()
