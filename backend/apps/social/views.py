"""
社交视图控制器 - 薄层HTTP处理
职责：处理社交相关的HTTP请求响应，调用业务服务，返回标准化响应
"""

import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.request import Request
from rest_framework.response import Response
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes

from apps.core.responses import APIResponse, SuccessMessages
from apps.core.exceptions import handle_exceptions
from .services import SocialService
from .serializers import (
    FriendRequestSerializer,
    FriendshipResponseSerializer,
    FollowSerializer,
    GroupCreateSerializer,
    GroupSerializer,
    GroupJoinSerializer
)

logger = logging.getLogger(__name__)


@extend_schema_view(
    list=extend_schema(
        summary="获取好友关系列表",
        description="获取当前用户的好友关系列表，包括已接受的好友和待处理的好友请求",
        tags=["社交功能"]
    ),
    create=extend_schema(
        summary="发送好友请求",
        description="向指定用户发送好友请求",
        tags=["社交功能"]
    ),
    retrieve=extend_schema(
        summary="获取好友关系详情",
        description="获取指定好友关系的详细信息",
        tags=["社交功能"]
    ),
    update=extend_schema(
        summary="处理好友请求",
        description="接受或拒绝好友请求",
        tags=["社交功能"]
    ),
    destroy=extend_schema(
        summary="删除好友关系",
        description="删除好友关系或取消好友请求",
        tags=["社交功能"]
    )
)
class FriendshipViewSet(viewsets.ViewSet):
    """
    好友关系视图集

    职责：
    1. 处理好友请求相关的HTTP请求
    2. 调用社交业务服务
    3. 返回标准化响应
    """

    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.social_service = SocialService()
    
    @action(detail=False, methods=['post'])
    def send_request(self, request: Request) -> Response:
        """
        发送好友请求
        
        POST /api/v1/friendships/send_request/
        """
        logger.info(f"发送好友请求: {request.user.username}")
        
        # 数据验证
        serializer = FriendRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.social_service.send_friend_request(
            str(request.user.uuid),
            serializer.validated_data['addressee_id'],
            serializer.validated_data.get('message', '')
        )
        
        return APIResponse.success(
            data=result,
            message="好友请求发送成功"
        )
    
    @action(detail=True, methods=['post'])
    def respond(self, request: Request, pk=None) -> Response:
        """
        响应好友请求
        
        POST /api/v1/friendships/{id}/respond/
        """
        logger.info(f"响应好友请求: {request.user.username} - {pk}")
        
        # 数据验证
        serializer = FriendshipResponseSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.social_service.respond_to_friend_request(
            pk,
            str(request.user.uuid),
            serializer.validated_data['accept']
        )
        
        return APIResponse.success(
            data=result,
            message=result.get('message', '好友请求处理成功')
        )
    
    @action(detail=False, methods=['get'])
    def friends(self, request: Request) -> Response:
        """
        获取好友列表
        
        GET /api/v1/friendships/friends/
        """
        # 调用业务服务
        result = self.social_service.get_friends(str(request.user.uuid))
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['get'])
    def requests(self, request: Request) -> Response:
        """
        获取好友请求列表
        
        GET /api/v1/friendships/requests/?type=received
        """
        request_type = request.query_params.get('type', 'received')
        
        # 调用业务服务
        result = self.social_service.get_friend_requests(
            str(request.user.uuid),
            request_type
        )
        
        return APIResponse.success(data=result)


@extend_schema_view(
    list=extend_schema(
        summary="获取关注关系列表",
        description="获取当前用户的关注和粉丝列表",
        tags=["社交功能"]
    ),
    create=extend_schema(
        summary="关注用户",
        description="关注指定用户",
        tags=["社交功能"]
    ),
    destroy=extend_schema(
        summary="取消关注",
        description="取消关注指定用户",
        tags=["社交功能"]
    )
)
class FollowViewSet(viewsets.ViewSet):
    """关注关系视图集"""

    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.social_service = SocialService()
    
    @action(detail=False, methods=['post'])
    def follow(self, request: Request) -> Response:
        """
        关注用户
        
        POST /api/v1/follows/follow/
        """
        logger.info(f"关注用户请求: {request.user.username}")
        
        # 数据验证
        serializer = FollowSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.social_service.follow_user(
            str(request.user.uuid),
            serializer.validated_data['following_id']
        )
        
        return APIResponse.success(
            data=result,
            message="关注成功"
        )
    
    @action(detail=False, methods=['post'])
    def unfollow(self, request: Request) -> Response:
        """
        取消关注用户
        
        POST /api/v1/follows/unfollow/
        """
        logger.info(f"取消关注用户请求: {request.user.username}")
        
        # 数据验证
        serializer = FollowSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.social_service.unfollow_user(
            str(request.user.uuid),
            serializer.validated_data['following_id']
        )
        
        return APIResponse.success(
            data=result,
            message=result.get('message', '取消关注成功')
        )
    
    @action(detail=False, methods=['get'])
    def followers(self, request: Request) -> Response:
        """
        获取粉丝列表
        
        GET /api/v1/follows/followers/
        """
        # 调用业务服务
        result = self.social_service.get_followers(str(request.user.uuid))
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['get'])
    def following(self, request: Request) -> Response:
        """
        获取关注列表
        
        GET /api/v1/follows/following/
        """
        # 调用业务服务
        result = self.social_service.get_following(str(request.user.uuid))
        
        return APIResponse.success(data=result)


@extend_schema_view(
    list=extend_schema(
        summary="获取群组列表",
        description="获取系统中的群组列表，支持搜索和筛选",
        tags=["社交功能"]
    ),
    create=extend_schema(
        summary="创建群组",
        description="创建新的群组",
        tags=["社交功能"]
    ),
    retrieve=extend_schema(
        summary="获取群组详情",
        description="获取指定群组的详细信息",
        tags=["社交功能"]
    ),
    update=extend_schema(
        summary="更新群组信息",
        description="更新群组的基本信息（仅群主和管理员）",
        tags=["社交功能"]
    ),
    destroy=extend_schema(
        summary="删除群组",
        description="删除群组（仅群主）",
        tags=["社交功能"]
    )
)
class GroupViewSet(viewsets.ModelViewSet):
    """群组视图集"""

    serializer_class = GroupSerializer
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.social_service = SocialService()
    def create(self, request: Request) -> Response:
        """
        创建群组
        
        POST /api/v1/groups/
        """
        logger.info(f"创建群组请求: {request.user.username}")
        
        # 数据验证
        serializer = GroupCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 调用业务服务
        result = self.social_service.create_group(
            str(request.user.uuid),
            serializer.validated_data
        )
        
        return APIResponse.created(
            data=result,
            message="群组创建成功"
        )
    def retrieve(self, request: Request, pk=None) -> Response:
        """
        获取群组详情
        
        GET /api/v1/groups/{id}/
        """
        user_id = str(request.user.uuid) if request.user.is_authenticated else None
        
        # 调用业务服务
        result = self.social_service.get_group_by_id(pk, user_id)
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['post'])
    def join(self, request: Request, pk=None) -> Response:
        """
        加入群组
        
        POST /api/v1/groups/{id}/join/
        """
        logger.info(f"加入群组请求: {request.user.username} - {pk}")
        
        # 调用业务服务
        result = self.social_service.join_group(pk, str(request.user.uuid))
        
        return APIResponse.success(
            data=result,
            message=result.get('message', '加入群组成功')
        )
    
    @action(detail=True, methods=['post'])
    def leave(self, request: Request, pk=None) -> Response:
        """
        离开群组
        
        POST /api/v1/groups/{id}/leave/
        """
        logger.info(f"离开群组请求: {request.user.username} - {pk}")
        
        # TODO: 实现离开群组逻辑
        return APIResponse.success(message="离开群组功能开发中")
    
    @action(detail=True, methods=['get'])
    def members(self, request: Request, pk=None) -> Response:
        """
        获取群组成员列表
        
        GET /api/v1/groups/{id}/members/
        """
        # TODO: 实现获取群组成员逻辑
        return APIResponse.success(message="获取群组成员功能开发中")
    
    @action(detail=False, methods=['get'])
    def my_groups(self, request: Request) -> Response:
        """
        获取我的群组列表
        
        GET /api/v1/groups/my_groups/?role=owner
        """
        # TODO: 实现获取我的群组逻辑
        return APIResponse.success(message="获取我的群组功能开发中")


@extend_schema_view(
    list=extend_schema(
        summary="获取推荐列表",
        description="获取个性化推荐内容，包括用户推荐、群组推荐等",
        tags=["社交功能"]
    )
)
class RecommendationViewSet(viewsets.ViewSet):
    """推荐视图集"""

    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.social_service = SocialService()
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def friends(self, request: Request) -> Response:
        """
        获取好友推荐
        
        GET /api/v1/recommendations/friends/?limit=10
        """
        limit = min(int(request.query_params.get('limit', 10)), 50)
        
        # 调用业务服务
        result = self.social_service.get_user_recommendations(
            str(request.user.uuid),
            'friend',
            limit
        )
        
        return APIResponse.success(data=result)
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def follows(self, request: Request) -> Response:
        """
        获取关注推荐
        
        GET /api/v1/recommendations/follows/?limit=10
        """
        limit = min(int(request.query_params.get('limit', 10)), 50)
        
        # 调用业务服务
        result = self.social_service.get_user_recommendations(
            str(request.user.uuid),
            'follow',
            limit
        )
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['post'])
    def dismiss(self, request: Request, pk=None) -> Response:
        """
        忽略推荐
        
        POST /api/v1/recommendations/{id}/dismiss/
        """
        from .models import UserRecommendation
        
        try:
            recommendation = UserRecommendation.objects.get(
                uuid=pk,
                user=request.user
            )
            recommendation.dismiss()
            
            return APIResponse.success(message="推荐已忽略")
            
        except UserRecommendation.DoesNotExist:
            return APIResponse.not_found(message="推荐不存在")
    
    @action(detail=False, methods=['post'])
    def generate(self, request: Request) -> Response:
        """
        生成推荐
        
        POST /api/v1/recommendations/generate/
        """
        recommendation_type = request.data.get('type', 'friend')
        
        if recommendation_type == 'friend':
            count = self.social_service.generate_friend_recommendations(str(request.user.uuid))
            return APIResponse.success(
                data={'generated_count': count},
                message=f"生成了 {count} 个好友推荐"
            )
        
        return APIResponse.validation_error(
            errors={'type': ['不支持的推荐类型']},
            message="参数验证失败"
        )


@extend_schema_view(
    list=extend_schema(
        summary="获取社交统计",
        description="获取用户的社交统计数据，包括好友数、关注数、粉丝数等",
        tags=["社交功能"]
    )
)
class SocialStatsViewSet(viewsets.ViewSet):
    """社交统计视图集"""

    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.social_service = SocialService()
    
    @action(detail=False, methods=['get'])
    @method_decorator(cache_page(60 * 10))  # 缓存10分钟
    def overview(self, request: Request) -> Response:
        """
        获取社交统计概览
        
        GET /api/v1/social-stats/overview/
        """
        # 调用业务服务
        result = self.social_service.get_social_stats(str(request.user.uuid))
        
        return APIResponse.success(data=result)
    
    @action(detail=True, methods=['get'], permission_classes=[AllowAny])
    @method_decorator(cache_page(60 * 30))  # 缓存30分钟
    def user_stats(self, request: Request, pk=None) -> Response:
        """
        获取用户社交统计（公开）
        
        GET /api/v1/social-stats/{user_id}/user_stats/
        """
        # 调用业务服务
        result = self.social_service.get_social_stats(pk)
        
        # 过滤敏感信息（公开接口）
        public_stats = {
            'user_id': result['user_id'],
            'friends_count': result['friends_count'],
            'followers_count': result['followers_count'],
            'owned_groups_count': result['owned_groups_count'],
            'total_social_connections': result['total_social_connections']
        }
        
        return APIResponse.success(data=public_stats)


@extend_schema_view(
    list=extend_schema(
        summary="获取社交活动列表",
        description="获取社交活动动态，包括好友动态、群组活动等",
        tags=["社交功能"]
    ),
    retrieve=extend_schema(
        summary="获取社交活动详情",
        description="获取指定社交活动的详细信息",
        tags=["社交功能"]
    )
)
class SocialActivityViewSet(viewsets.ReadOnlyModelViewSet):
    """社交活动视图集"""

    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def timeline(self, request: Request) -> Response:
        """
        获取社交活动时间线
        
        GET /api/v1/social-activities/timeline/?limit=20
        """
        from .models import SocialActivity, Friendship, Follow
        
        limit = min(int(request.query_params.get('limit', 20)), 100)
        
        # 获取用户的好友和关注的人
        friend_ids = Friendship.objects.filter(
            models.Q(requester=request.user) | models.Q(addressee=request.user),
            status='accepted'
        ).values_list('requester_id', 'addressee_id')
        
        user_friend_ids = set()
        for req_id, addr_id in friend_ids:
            user_friend_ids.add(req_id if req_id != request.user.id else addr_id)
        
        following_ids = Follow.objects.filter(
            follower=request.user
        ).values_list('following_id', flat=True)
        
        # 合并关注的用户ID
        relevant_user_ids = user_friend_ids.union(set(following_ids))
        relevant_user_ids.add(request.user.id)  # 包含自己
        
        # 获取相关用户的公开活动
        activities = SocialActivity.objects.filter(
            user_id__in=relevant_user_ids,
            is_public=True
        ).select_related('user', 'target_user', 'target_group').order_by('-created_at')[:limit]
        
        data = []
        for activity in activities:
            activity_data = {
                'id': str(activity.uuid),
                'user': {
                    'id': str(activity.user.uuid),
                    'username': activity.user.username,
                    'display_name': activity.user.display_name
                },
                'activity_type': activity.activity_type,
                'description': activity.description,
                'created_at': activity.created_at.isoformat()
            }
            
            if activity.target_user:
                activity_data['target_user'] = {
                    'id': str(activity.target_user.uuid),
                    'username': activity.target_user.username,
                    'display_name': activity.target_user.display_name
                }
            
            if activity.target_group:
                activity_data['target_group'] = {
                    'id': str(activity.target_group.uuid),
                    'name': activity.target_group.name
                }
            
            data.append(activity_data)
        
        return APIResponse.success(data=data)
    
    @action(detail=False, methods=['get'])
    def my_activities(self, request: Request) -> Response:
        """
        获取我的社交活动
        
        GET /api/v1/social-activities/my_activities/?limit=20
        """
        from .models import SocialActivity
        
        limit = min(int(request.query_params.get('limit', 20)), 100)
        
        activities = SocialActivity.objects.filter(
            user=request.user
        ).select_related('target_user', 'target_group').order_by('-created_at')[:limit]
        
        data = []
        for activity in activities:
            activity_data = {
                'id': str(activity.uuid),
                'activity_type': activity.activity_type,
                'description': activity.description,
                'is_public': activity.is_public,
                'created_at': activity.created_at.isoformat()
            }
            
            if activity.target_user:
                activity_data['target_user'] = {
                    'id': str(activity.target_user.uuid),
                    'username': activity.target_user.username,
                    'display_name': activity.target_user.display_name
                }
            
            if activity.target_group:
                activity_data['target_group'] = {
                    'id': str(activity.target_group.uuid),
                    'name': activity.target_group.name
                }
            
            data.append(activity_data)
        
        return APIResponse.success(data=data)