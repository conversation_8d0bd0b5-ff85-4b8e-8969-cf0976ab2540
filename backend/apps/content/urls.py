"""
内容URL路由配置 - 独立的内容域路由
职责：定义内容相关的API路由，支持微服务化部署
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    PostViewSet,
    CommentViewSet,
    ContentCategoryViewSet,
    ContentStatsViewSet
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'posts', PostViewSet, basename='post')
router.register(r'comments', CommentViewSet, basename='comment')
router.register(r'content-categories', ContentCategoryViewSet, basename='contentcategory')
router.register(r'content-stats', ContentStatsViewSet, basename='contentstats')

# URL模式
urlpatterns = [
    # API路由 - 不需要重复的api/v1/前缀
    path('', include(router.urls)),
]

# 应用命名空间
app_name = 'content'
