@echo off
REM ========================================
REM SOIC 社交创新社区 - 增强启动脚本
REM 包含完整的环境检查和启动流程
REM ========================================

chcp 65001 >nul
title SOIC 社交创新社区 - 启动脚本

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    SOIC 社交创新社区                          ║
echo ║                  Social Innovation Community                 ║
echo ║                                                              ║
echo ║                    🚀 增强启动脚本                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.9+
    pause
    exit /b 1
)

REM 显示Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

REM 检查是否在项目根目录
if not exist "manage.py" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    echo 当前目录: %CD%
    pause
    exit /b 1
)
echo ✅ 项目目录检查通过

REM 检查虚拟环境
echo.
echo 🔍 检查虚拟环境...
python -c "import sys; print('虚拟环境' if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) else '系统环境')"

REM 设置环境变量
echo.
echo 🔧 设置环境变量...
set DJANGO_SETTINGS_MODULE=config.settings.local
echo ✅ DJANGO_SETTINGS_MODULE=%DJANGO_SETTINGS_MODULE%

REM 检查依赖
echo.
echo 🔍 检查关键依赖...
python -c "import django; print('✅ Django版本:', django.get_version())" 2>nul || (
    echo ❌ Django未安装，正在安装依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 运行数据库迁移
echo.
echo 🔍 检查数据库迁移...
python manage.py migrate --check >nul 2>&1
if errorlevel 1 (
    echo ⚠️  发现未应用的迁移，正在执行...
    python manage.py migrate
    if errorlevel 1 (
        echo ❌ 数据库迁移失败
        pause
        exit /b 1
    )
    echo ✅ 数据库迁移完成
) else (
    echo ✅ 数据库迁移检查通过
)

REM 收集静态文件（生产环境）
if "%1"=="--production" (
    echo.
    echo 🔍 收集静态文件...
    python manage.py collectstatic --noinput
    if errorlevel 1 (
        echo ❌ 静态文件收集失败
        pause
        exit /b 1
    )
    echo ✅ 静态文件收集完成
)

REM 测试启动管理器
echo.
echo 🧪 测试启动管理器...
python scripts/test_startup.py
if errorlevel 1 (
    echo ⚠️  启动管理器测试失败，但继续启动服务器...
) else (
    echo ✅ 启动管理器测试通过
)

REM 启动开发服务器
echo.
echo ========================================
echo 🚀 启动 SOIC 社交创新社区开发服务器
echo ========================================
echo.
echo 📍 服务器访问地址:
echo    • 主页: http://localhost:8000/
echo    • API文档: http://localhost:8000/api/docs/
echo    • 管理后台: http://localhost:8000/admin/
echo    • 健康检查: http://localhost:8000/api/v1/core/health/
echo.
echo 💡 使用提示:
echo    • 按 Ctrl+C 停止服务器
echo    • 查看实时日志: tail -f logs/django.log
echo    • 如遇问题请查看 README.md
echo.
echo ========================================
echo 正在启动服务器，请稍候...
echo ========================================

REM 启动服务器
python manage.py runserver 0.0.0.0:8000

echo.
echo 👋 服务器已停止
pause
