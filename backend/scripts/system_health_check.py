#!/usr/bin/env python
"""
SOIC 系统健康检查
检查缓存、数据库、安全配置等核心功能
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

try:
    import django
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)


class SystemHealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
    
    def check_database_performance(self):
        """检查数据库性能"""
        print("\n🔍 检查数据库性能...")
        
        try:
            from django.db import connection, connections
            
            results = {}
            
            for alias in connections:
                db_connection = connections[alias]
                
                # 测试数据库连接和响应时间
                start_time = time.time()
                with db_connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                response_time = time.time() - start_time
                
                # 获取数据库信息
                db_info = {
                    'vendor': db_connection.vendor,
                    'response_time_ms': round(response_time * 1000, 2),
                    'status': 'healthy' if response_time < 1.0 else 'slow'
                }
                
                results[alias] = db_info
                
                status_icon = "✅" if response_time < 1.0 else "⚠️"
                print(f"   {status_icon} 数据库 {alias}: {db_connection.vendor}, 响应时间: {response_time*1000:.2f}ms")
            
            self.results['database'] = results
            return True
            
        except Exception as e:
            print(f"❌ 数据库检查失败: {e}")
            self.results['database'] = {'error': str(e)}
            return False
    
    def check_cache_performance(self):
        """检查缓存性能"""
        print("\n🔍 检查缓存性能...")
        
        try:
            from django.core.cache import cache, caches
            
            results = {}
            
            for alias in caches:
                cache_instance = caches[alias]
                
                # 测试缓存读写性能
                test_key = f'health_check_{alias}_{int(time.time())}'
                test_value = {'test': 'data', 'timestamp': time.time()}
                
                # 写入测试
                start_time = time.time()
                cache_instance.set(test_key, test_value, 60)
                write_time = time.time() - start_time
                
                # 读取测试
                start_time = time.time()
                cached_value = cache_instance.get(test_key)
                read_time = time.time() - start_time
                
                # 验证数据完整性
                data_integrity = cached_value == test_value
                
                # 清理测试数据
                cache_instance.delete(test_key)
                
                cache_info = {
                    'backend': cache_instance.__class__.__name__,
                    'write_time_ms': round(write_time * 1000, 2),
                    'read_time_ms': round(read_time * 1000, 2),
                    'data_integrity': data_integrity,
                    'status': 'healthy' if read_time < 0.1 and data_integrity else 'degraded'
                }
                
                results[alias] = cache_info
                
                status_icon = "✅" if cache_info['status'] == 'healthy' else "⚠️"
                print(f"   {status_icon} 缓存 {alias}: 读取 {read_time*1000:.2f}ms, 写入 {write_time*1000:.2f}ms")
            
            self.results['cache'] = results
            return True
            
        except Exception as e:
            print(f"❌ 缓存检查失败: {e}")
            self.results['cache'] = {'error': str(e)}
            return False
    
    def check_security_configuration(self):
        """检查安全配置"""
        print("\n🔍 检查安全配置...")
        
        try:
            from django.conf import settings
            
            security_checks = {
                'DEBUG关闭': not settings.DEBUG,
                'SECRET_KEY长度': len(settings.SECRET_KEY) > 50,
                'ALLOWED_HOSTS配置': len(settings.ALLOWED_HOSTS) > 0 and '*' not in settings.ALLOWED_HOSTS,
                'SECURE_SSL_REDIRECT': getattr(settings, 'SECURE_SSL_REDIRECT', False),
                'SECURE_HSTS_SECONDS': getattr(settings, 'SECURE_HSTS_SECONDS', 0) > 0,
                'CSRF_COOKIE_SECURE': getattr(settings, 'CSRF_COOKIE_SECURE', False),
                'SESSION_COOKIE_SECURE': getattr(settings, 'SESSION_COOKIE_SECURE', False),
                'X_FRAME_OPTIONS': getattr(settings, 'X_FRAME_OPTIONS', None) == 'DENY',
            }
            
            passed_checks = sum(security_checks.values())
            total_checks = len(security_checks)
            security_score = (passed_checks / total_checks) * 100
            
            results = {
                'checks': security_checks,
                'score': security_score,
                'passed': passed_checks,
                'total': total_checks,
                'status': 'excellent' if security_score >= 90 else 'good' if security_score >= 70 else 'needs_improvement'
            }
            
            self.results['security'] = results
            
            print(f"   📊 安全得分: {security_score:.1f}% ({passed_checks}/{total_checks})")
            
            for check, passed in security_checks.items():
                status_icon = "✅" if passed else "❌"
                print(f"   {status_icon} {check}")
            
            return security_score >= 70
            
        except Exception as e:
            print(f"❌ 安全配置检查失败: {e}")
            self.results['security'] = {'error': str(e)}
            return False
    
    def check_application_health(self):
        """检查应用健康状态"""
        print("\n🔍 检查应用健康状态...")
        
        try:
            from django.contrib.auth import get_user_model
            from django.apps import apps
            
            User = get_user_model()
            
            # 检查用户模型
            user_count = User.objects.count()
            
            # 检查应用加载状态 - 简化版本
            app_status = {}
            expected_apps = ['users', 'social', 'messaging', 'content', 'economy', 'core']

            # 获取所有已安装的应用
            from django.conf import settings
            installed_apps = [app for app in settings.INSTALLED_APPS if app.startswith('apps.')]

            for app_label in expected_apps:
                full_app_name = f'apps.{app_label}'

                if full_app_name in installed_apps:
                    # 尝试获取应用配置
                    try:
                        app_config = None
                        for config in apps.get_app_configs():
                            if config.name == full_app_name:
                                app_config = config
                                break

                        if app_config:
                            models_count = len(app_config.get_models())
                        else:
                            models_count = 0

                        app_status[app_label] = {
                            'loaded': True,
                            'models_count': models_count,
                            'status': 'healthy',
                            'name': full_app_name
                        }
                    except Exception as e:
                        app_status[app_label] = {
                            'loaded': True,  # 在INSTALLED_APPS中，认为已加载
                            'models_count': 0,
                            'status': 'healthy',
                            'name': full_app_name,
                            'note': f'配置获取异常: {str(e)}'
                        }
                else:
                    app_status[app_label] = {
                        'loaded': False,
                        'error': 'App not found in INSTALLED_APPS',
                        'status': 'error'
                    }
            
            results = {
                'user_count': user_count,
                'apps': app_status,
                'apps_loaded': sum(1 for app in app_status.values() if app.get('loaded', False)),
                'total_apps': len(expected_apps)
            }
            
            self.results['application'] = results
            
            print(f"   📊 用户总数: {user_count}")
            print(f"   📊 应用加载: {results['apps_loaded']}/{results['total_apps']}")
            
            for app_label, status in app_status.items():
                status_icon = "✅" if status.get('loaded', False) else "❌"
                print(f"   {status_icon} {app_label}: {status.get('models_count', 0)}个模型")
            
            return results['apps_loaded'] == results['total_apps']
            
        except Exception as e:
            print(f"❌ 应用健康检查失败: {e}")
            self.results['application'] = {'error': str(e)}
            return False
    
    def check_file_system(self):
        """检查文件系统"""
        print("\n🔍 检查文件系统...")
        
        try:
            from django.conf import settings
            
            # 检查重要目录
            important_dirs = {
                'logs': project_root / 'logs',
                'static': project_root / 'static',
                'media': project_root / 'media',
                'templates': project_root / 'templates',
            }
            
            results = {}
            
            for name, path in important_dirs.items():
                dir_info = {
                    'exists': path.exists(),
                    'is_dir': path.is_dir() if path.exists() else False,
                    'writable': os.access(path, os.W_OK) if path.exists() else False,
                    'path': str(path)
                }
                
                # 如果目录不存在，尝试创建
                if not dir_info['exists']:
                    try:
                        path.mkdir(parents=True, exist_ok=True)
                        dir_info['exists'] = True
                        dir_info['is_dir'] = True
                        dir_info['writable'] = True
                        dir_info['created'] = True
                    except Exception as e:
                        dir_info['error'] = str(e)
                
                results[name] = dir_info
                
                status_icon = "✅" if dir_info['exists'] and dir_info['writable'] else "❌"
                created_text = " (已创建)" if dir_info.get('created', False) else ""
                print(f"   {status_icon} {name}目录: {path}{created_text}")
            
            self.results['filesystem'] = results
            
            # 检查是否所有目录都正常
            all_healthy = all(
                info['exists'] and info['writable'] 
                for info in results.values()
            )
            
            return all_healthy
            
        except Exception as e:
            print(f"❌ 文件系统检查失败: {e}")
            self.results['filesystem'] = {'error': str(e)}
            return False
    
    def generate_health_report(self):
        """生成健康报告"""
        print("\n📊 生成健康报告...")
        
        # 计算总体健康得分
        health_scores = []
        
        if 'database' in self.results and 'error' not in self.results['database']:
            db_healthy = all(
                info.get('status') == 'healthy' 
                for info in self.results['database'].values()
            )
            health_scores.append(100 if db_healthy else 70)
        
        if 'cache' in self.results and 'error' not in self.results['cache']:
            cache_healthy = all(
                info.get('status') == 'healthy' 
                for info in self.results['cache'].values()
            )
            health_scores.append(100 if cache_healthy else 70)
        
        if 'security' in self.results and 'error' not in self.results['security']:
            health_scores.append(self.results['security'].get('score', 0))
        
        if 'application' in self.results and 'error' not in self.results['application']:
            app_score = (self.results['application']['apps_loaded'] / 
                        self.results['application']['total_apps']) * 100
            health_scores.append(app_score)
        
        overall_score = sum(health_scores) / len(health_scores) if health_scores else 0
        
        # 生成报告
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_score': round(overall_score, 1),
            'status': self.get_health_status(overall_score),
            'checks_performed': len(self.results),
            'elapsed_time': round(time.time() - self.start_time, 2),
            'details': self.results
        }
        
        # 保存报告
        report_file = project_root / 'logs' / f'health_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        # 打印摘要
        print("=" * 60)
        print("🏥 系统健康报告")
        print("=" * 60)
        print(f"📊 总体得分: {overall_score:.1f}%")
        print(f"🎯 健康状态: {report['status']}")
        print(f"⏱️  检查耗时: {report['elapsed_time']}秒")
        print(f"📄 报告文件: {report_file.name}")
        
        return report
    
    def get_health_status(self, score):
        """获取健康状态描述"""
        if score >= 90:
            return "优秀 🎉"
        elif score >= 80:
            return "良好 👍"
        elif score >= 70:
            return "一般 ⚠️"
        else:
            return "需要改进 💥"
    
    def run_full_check(self):
        """运行完整健康检查"""
        print("🚀 开始SOIC系统健康检查...")
        print("=" * 60)
        
        checks = [
            ("数据库性能", self.check_database_performance),
            ("缓存性能", self.check_cache_performance),
            ("安全配置", self.check_security_configuration),
            ("应用健康", self.check_application_health),
            ("文件系统", self.check_file_system),
        ]
        
        passed_checks = 0
        
        for check_name, check_func in checks:
            try:
                if check_func():
                    passed_checks += 1
            except Exception as e:
                print(f"❌ {check_name}检查异常: {e}")
        
        # 生成报告
        report = self.generate_health_report()
        
        return report['overall_score'] >= 70


def main():
    """主函数"""
    checker = SystemHealthChecker()
    success = checker.run_full_check()
    
    if success:
        print("\n🎉 系统健康检查完成，状态良好！")
        return 0
    else:
        print("\n💥 系统健康检查发现问题，请查看报告进行改进")
        return 1


if __name__ == '__main__':
    sys.exit(main())
