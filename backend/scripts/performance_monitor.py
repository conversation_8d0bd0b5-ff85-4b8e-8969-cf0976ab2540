#!/usr/bin/env python
"""
SOIC 性能监控脚本
监控系统性能、缓存效率、安全状态等
"""

import os
import sys
import time
import json
import psutil
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

try:
    import django
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.start_time = time.time()
    
    def collect_system_metrics(self):
        """收集系统指标"""
        print("\n🔍 收集系统性能指标...")
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 网络统计
        network = psutil.net_io_counters()
        
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'cpu': {
                'percent': cpu_percent,
                'count': cpu_count,
                'load_avg': os.getloadavg() if hasattr(os, 'getloadavg') else None
            },
            'memory': {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used,
                'free': memory.free
            },
            'disk': {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': disk.percent
            },
            'network': {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
        }
        
        self.metrics['system'].append(metrics)
        
        print(f"📊 CPU使用率: {cpu_percent}%")
        print(f"📊 内存使用率: {memory.percent}%")
        print(f"📊 磁盘使用率: {disk.percent}%")
        
        return metrics
    
    def collect_database_metrics(self):
        """收集数据库指标"""
        print("\n🔍 收集数据库性能指标...")
        
        try:
            from django.db import connection, connections
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'connections': {}
            }
            
            for alias in connections:
                db_connection = connections[alias]
                
                # 获取数据库连接信息
                connection_info = {
                    'vendor': db_connection.vendor,
                    'queries_count': len(db_connection.queries) if hasattr(db_connection, 'queries') else 0,
                }
                
                # 执行测试查询来检查响应时间
                start_time = time.time()
                with db_connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                query_time = time.time() - start_time
                
                connection_info['response_time'] = query_time
                metrics['connections'][alias] = connection_info
                
                print(f"📊 数据库 {alias}: {db_connection.vendor}, 响应时间: {query_time*1000:.2f}ms")
            
            self.metrics['database'].append(metrics)
            return metrics
            
        except Exception as e:
            print(f"❌ 数据库指标收集失败: {e}")
            return None
    
    def collect_cache_metrics(self):
        """收集缓存指标"""
        print("\n🔍 收集缓存性能指标...")
        
        try:
            from django.core.cache import cache, caches
            from apps.core.cache import cache_manager
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'caches': {}
            }
            
            for alias in caches:
                cache_instance = caches[alias]
                
                # 测试缓存性能
                test_key = f'performance_test_{alias}_{int(time.time())}'
                test_value = {'test': 'data', 'timestamp': time.time()}
                
                # 写入测试
                start_time = time.time()
                cache_instance.set(test_key, test_value, 60)
                write_time = time.time() - start_time
                
                # 读取测试
                start_time = time.time()
                cached_value = cache_instance.get(test_key)
                read_time = time.time() - start_time
                
                # 清理测试数据
                cache_instance.delete(test_key)
                
                cache_info = {
                    'backend': cache_instance.__class__.__name__,
                    'write_time': write_time,
                    'read_time': read_time,
                    'hit': cached_value is not None
                }
                
                metrics['caches'][alias] = cache_info
                
                print(f"📊 缓存 {alias}: 写入 {write_time*1000:.2f}ms, 读取 {read_time*1000:.2f}ms")
            
            self.metrics['cache'].append(metrics)
            return metrics
            
        except Exception as e:
            print(f"❌ 缓存指标收集失败: {e}")
            return None
    
    def collect_application_metrics(self):
        """收集应用指标"""
        print("\n🔍 收集应用性能指标...")
        
        try:
            from django.contrib.auth import get_user_model
            from django.apps import apps
            
            User = get_user_model()
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'users': {
                    'total': User.objects.count(),
                    'active_today': User.objects.filter(
                        last_login__gte=datetime.now() - timedelta(days=1)
                    ).count() if hasattr(User, 'last_login') else 0,
                },
                'models': {}
            }
            
            # 收集各个模型的数据量
            for app_config in apps.get_app_configs():
                if app_config.name.startswith('apps.'):
                    app_metrics = {}
                    for model in app_config.get_models():
                        try:
                            count = model.objects.count()
                            app_metrics[model.__name__] = count
                        except Exception:
                            app_metrics[model.__name__] = 'N/A'
                    
                    if app_metrics:
                        metrics['models'][app_config.label] = app_metrics
            
            self.metrics['application'].append(metrics)
            
            print(f"📊 用户总数: {metrics['users']['total']}")
            print(f"📊 今日活跃用户: {metrics['users']['active_today']}")
            
            return metrics
            
        except Exception as e:
            print(f"❌ 应用指标收集失败: {e}")
            return None
    
    def check_security_status(self):
        """检查安全状态"""
        print("\n🔍 检查系统安全状态...")
        
        try:
            from django.conf import settings
            
            security_checks = {
                'DEBUG': not settings.DEBUG,
                'SECRET_KEY_LENGTH': len(settings.SECRET_KEY) > 50,
                'ALLOWED_HOSTS': len(settings.ALLOWED_HOSTS) > 0 and '*' not in settings.ALLOWED_HOSTS,
                'SECURE_SSL_REDIRECT': getattr(settings, 'SECURE_SSL_REDIRECT', False),
                'SECURE_HSTS_SECONDS': getattr(settings, 'SECURE_HSTS_SECONDS', 0) > 0,
                'CSRF_COOKIE_SECURE': getattr(settings, 'CSRF_COOKIE_SECURE', False),
                'SESSION_COOKIE_SECURE': getattr(settings, 'SESSION_COOKIE_SECURE', False),
            }
            
            passed_checks = sum(security_checks.values())
            total_checks = len(security_checks)
            security_score = (passed_checks / total_checks) * 100
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'checks': security_checks,
                'score': security_score,
                'passed': passed_checks,
                'total': total_checks
            }
            
            self.metrics['security'].append(metrics)
            
            print(f"📊 安全得分: {security_score:.1f}% ({passed_checks}/{total_checks})")
            
            for check, passed in security_checks.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {check}")
            
            return metrics
            
        except Exception as e:
            print(f"❌ 安全状态检查失败: {e}")
            return None
    
    def analyze_performance(self):
        """分析性能数据"""
        print("\n📈 性能分析...")
        
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'recommendations': [],
            'warnings': [],
            'critical_issues': []
        }
        
        # 分析系统指标
        if self.metrics['system']:
            latest_system = self.metrics['system'][-1]
            
            if latest_system['cpu']['percent'] > 80:
                analysis['warnings'].append(f"CPU使用率过高: {latest_system['cpu']['percent']}%")
            
            if latest_system['memory']['percent'] > 80:
                analysis['warnings'].append(f"内存使用率过高: {latest_system['memory']['percent']}%")
            
            if latest_system['disk']['percent'] > 90:
                analysis['critical_issues'].append(f"磁盘空间不足: {latest_system['disk']['percent']}%")
        
        # 分析数据库指标
        if self.metrics['database']:
            latest_db = self.metrics['database'][-1]
            
            for alias, info in latest_db['connections'].items():
                if info['response_time'] > 1.0:  # 1秒
                    analysis['warnings'].append(f"数据库 {alias} 响应时间过长: {info['response_time']*1000:.0f}ms")
        
        # 分析缓存指标
        if self.metrics['cache']:
            latest_cache = self.metrics['cache'][-1]
            
            for alias, info in latest_cache['caches'].items():
                if info['read_time'] > 0.1:  # 100ms
                    analysis['warnings'].append(f"缓存 {alias} 读取时间过长: {info['read_time']*1000:.0f}ms")
        
        # 生成建议
        if not analysis['warnings'] and not analysis['critical_issues']:
            analysis['recommendations'].append("系统运行良好，继续保持")
        else:
            if analysis['warnings']:
                analysis['recommendations'].append("建议监控系统资源使用情况")
            if analysis['critical_issues']:
                analysis['recommendations'].append("立即处理关键问题")
        
        self.metrics['analysis'].append(analysis)
        
        # 打印分析结果
        if analysis['critical_issues']:
            print("🚨 关键问题:")
            for issue in analysis['critical_issues']:
                print(f"   • {issue}")
        
        if analysis['warnings']:
            print("⚠️  警告:")
            for warning in analysis['warnings']:
                print(f"   • {warning}")
        
        if analysis['recommendations']:
            print("💡 建议:")
            for rec in analysis['recommendations']:
                print(f"   • {rec}")
        
        return analysis
    
    def save_metrics(self, filename: str = None):
        """保存指标数据"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'performance_metrics_{timestamp}.json'
        
        filepath = project_root / 'logs' / filename
        filepath.parent.mkdir(exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(dict(self.metrics), f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 性能指标已保存到: {filepath}")
        return filepath
    
    def run_full_monitoring(self):
        """运行完整监控"""
        print("🚀 开始SOIC性能监控...")
        print("=" * 60)
        
        # 收集各项指标
        self.collect_system_metrics()
        self.collect_database_metrics()
        self.collect_cache_metrics()
        self.collect_application_metrics()
        self.check_security_status()
        
        # 分析性能
        self.analyze_performance()
        
        # 保存结果
        report_file = self.save_metrics()
        
        # 生成摘要
        elapsed_time = time.time() - self.start_time
        print("\n" + "=" * 60)
        print("📊 监控摘要:")
        print(f"   • 监控耗时: {elapsed_time:.2f}秒")
        print(f"   • 收集指标: {sum(len(v) for v in self.metrics.values())}项")
        print(f"   • 报告文件: {report_file.name}")
        
        return dict(self.metrics)


def main():
    """主函数"""
    monitor = PerformanceMonitor()
    metrics = monitor.run_full_monitoring()
    
    # 检查是否有关键问题
    if 'analysis' in metrics and metrics['analysis']:
        latest_analysis = metrics['analysis'][-1]
        if latest_analysis['critical_issues']:
            print("\n💥 发现关键问题，建议立即处理！")
            return 1
        elif latest_analysis['warnings']:
            print("\n⚠️  发现警告，建议关注系统状态")
            return 0
    
    print("\n🎉 性能监控完成，系统运行正常！")
    return 0


if __name__ == '__main__':
    sys.exit(main())
