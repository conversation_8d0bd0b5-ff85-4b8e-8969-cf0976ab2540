#!/usr/bin/env python
"""
测试启动管理器功能
用于验证启动检查是否正常工作
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

def test_startup_manager():
    """测试启动管理器"""
    print("\n" + "=" * 60)
    print("🧪 测试启动管理器功能")
    print("=" * 60)
    
    try:
        from apps.core.startup import StartupManager
        
        # 创建启动管理器实例
        manager = StartupManager()
        
        # 测试横幅打印
        print("\n📋 测试横幅打印:")
        manager.print_banner()
        
        # 测试各项检查
        print("\n📋 测试各项检查:")
        
        # Python版本检查
        result1 = manager.check_python_version()
        print(f"Python版本检查: {'✅' if result1 else '❌'}")
        
        # 环境变量检查
        result2 = manager.check_environment_variables()
        print(f"环境变量检查: {'✅' if result2 else '❌'}")
        
        # 必需目录检查
        result3 = manager.check_required_directories()
        print(f"必需目录检查: {'✅' if result3 else '❌'}")
        
        # 数据库连接检查
        result4 = manager.check_database_connection()
        print(f"数据库连接检查: {'✅' if result4 else '❌'}")
        
        # 缓存系统检查
        result5 = manager.check_cache_system()
        print(f"缓存系统检查: {'✅' if result5 else '❌'}")
        
        # 业务应用检查
        result6 = manager.check_business_apps()
        print(f"业务应用检查: {'✅' if result6 else '❌'}")
        
        # 打印摘要
        print("\n📋 测试摘要打印:")
        success = manager.print_summary()
        
        if success:
            manager.print_startup_info()
        
        return success
        
    except ImportError as e:
        print(f"❌ 无法导入启动管理器: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_run_startup_checks():
    """测试启动检查便捷函数"""
    print("\n" + "=" * 60)
    print("🧪 测试启动检查便捷函数")
    print("=" * 60)
    
    try:
        from apps.core.startup import run_startup_checks
        
        success = run_startup_checks()
        print(f"\n启动检查结果: {'✅ 成功' if success else '❌ 失败'}")
        return success
        
    except ImportError as e:
        print(f"❌ 无法导入启动检查函数: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试SOIC启动管理器")
    
    # 测试启动管理器类
    result1 = test_startup_manager()
    
    # 测试便捷函数
    result2 = test_run_startup_checks()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if result1 and result2:
        print("🎉 所有测试通过！启动管理器工作正常。")
        return 0
    else:
        print("💥 部分测试失败，请检查相关配置。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
