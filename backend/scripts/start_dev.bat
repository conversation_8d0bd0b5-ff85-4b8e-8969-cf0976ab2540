@echo off
REM SOIC开发环境启动脚本 (Windows)

echo ========================================
echo SOIC 开发环境启动脚本
echo ========================================

REM 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo 错误: 找不到虚拟环境，请先运行 setup.bat
    pause
    exit /b 1
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查环境变量文件
if not exist ".env" (
    echo 警告: 找不到.env文件，使用默认配置
    copy .env.example .env
)

REM 设置Django设置模块
set DJANGO_SETTINGS_MODULE=config.settings.local

REM 运行数据库迁移
echo 检查数据库迁移...
python manage.py migrate

REM 启动开发服务器
echo.
echo ========================================
echo 🚀 启动 SOIC 社交创新社区开发服务器
echo ========================================
echo.
echo 📍 服务器访问地址:
echo    • 主页: http://localhost:8000/
echo    • API文档: http://localhost:8000/api/docs/
echo    • 管理后台: http://localhost:8000/admin/
echo    • 健康检查: http://localhost:8000/api/v1/core/health/
echo.
echo 💡 使用提示:
echo    • 按 Ctrl+C 停止服务器
echo    • 查看实时日志: tail -f logs/django.log
echo    • 如遇问题请查看 README.md
echo.
echo ========================================
echo 正在启动服务器，请稍候...
echo ========================================

python manage.py runserver 0.0.0.0:8000

pause
