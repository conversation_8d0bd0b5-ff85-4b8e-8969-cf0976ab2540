#!/usr/bin/env python
"""
SOIC 系统优化脚本
执行系统优化、性能调优和部署准备
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

try:
    import django
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)


class SOICSystemOptimizer:
    """SOIC系统优化器"""
    
    def __init__(self):
        self.project_root = project_root
        self.optimization_results = {}
        self.performance_metrics = {}
    
    def print_header(self, title):
        """打印标题"""
        print(f"\n{'='*60}")
        print(f"⚡ {title}")
        print(f"{'='*60}")
    
    def optimize_database_queries(self):
        """优化数据库查询"""
        self.print_header("数据库查询优化")
        
        try:
            from django.db import connection
            from django.conf import settings
            
            # 检查数据库连接池配置
            db_config = settings.DATABASES['default']
            print(f"📊 数据库引擎: {db_config['ENGINE']}")
            print(f"📊 数据库名称: {db_config['NAME']}")
            
            # 检查慢查询
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                print("✅ 数据库连接正常")
            
            # 建议优化项
            optimizations = [
                "✅ 使用select_related()和prefetch_related()减少查询次数",
                "✅ 为常用查询字段添加数据库索引",
                "✅ 使用数据库连接池提高性能",
                "✅ 实施查询缓存策略",
                "✅ 定期分析和优化慢查询"
            ]
            
            print("💡 数据库优化建议:")
            for opt in optimizations:
                print(f"   {opt}")
            
            self.optimization_results['database'] = True
            
        except Exception as e:
            print(f"❌ 数据库优化检查失败: {e}")
            self.optimization_results['database'] = False
    
    def optimize_caching(self):
        """优化缓存配置"""
        self.print_header("缓存系统优化")
        
        try:
            from django.core.cache import cache
            from django.conf import settings
            
            # 测试缓存性能
            start_time = time.time()
            cache.set('test_key', 'test_value', 60)
            cached_value = cache.get('test_key')
            cache_time = time.time() - start_time
            
            if cached_value == 'test_value':
                print(f"✅ 缓存系统正常 (响应时间: {cache_time*1000:.2f}ms)")
                
                # 缓存配置建议
                cache_config = settings.CACHES['default']
                print(f"📊 缓存后端: {cache_config['BACKEND']}")
                
                optimizations = [
                    "✅ 为频繁访问的数据启用缓存",
                    "✅ 使用Redis作为生产环境缓存后端",
                    "✅ 实施分层缓存策略",
                    "✅ 设置合适的缓存过期时间",
                    "✅ 监控缓存命中率"
                ]
                
                print("💡 缓存优化建议:")
                for opt in optimizations:
                    print(f"   {opt}")
                
                self.optimization_results['caching'] = True
                self.performance_metrics['cache_response_time'] = cache_time
            else:
                print("❌ 缓存系统异常")
                self.optimization_results['caching'] = False
                
        except Exception as e:
            print(f"❌ 缓存优化检查失败: {e}")
            self.optimization_results['caching'] = False
    
    def optimize_static_files(self):
        """优化静态文件配置"""
        self.print_header("静态文件优化")
        
        try:
            from django.conf import settings
            
            static_root = getattr(settings, 'STATIC_ROOT', None)
            media_root = getattr(settings, 'MEDIA_ROOT', None)
            
            print(f"📊 静态文件目录: {static_root}")
            print(f"📊 媒体文件目录: {media_root}")
            
            # 检查静态文件目录
            if static_root and Path(static_root).exists():
                print("✅ 静态文件目录存在")
            else:
                print("⚠️  静态文件目录不存在，建议运行 collectstatic")
            
            # 静态文件优化建议
            optimizations = [
                "✅ 使用CDN加速静态文件访问",
                "✅ 启用Gzip压缩减少传输大小",
                "✅ 设置适当的缓存头",
                "✅ 压缩CSS和JavaScript文件",
                "✅ 优化图片格式和大小"
            ]
            
            print("💡 静态文件优化建议:")
            for opt in optimizations:
                print(f"   {opt}")
            
            self.optimization_results['static_files'] = True
            
        except Exception as e:
            print(f"❌ 静态文件优化检查失败: {e}")
            self.optimization_results['static_files'] = False
    
    def optimize_security(self):
        """安全配置优化"""
        self.print_header("安全配置优化")
        
        try:
            from django.conf import settings
            
            security_checks = {
                'DEBUG': not settings.DEBUG,
                'SECRET_KEY': len(settings.SECRET_KEY) > 50,
                'ALLOWED_HOSTS': len(settings.ALLOWED_HOSTS) > 0,
                'SECURE_SSL_REDIRECT': getattr(settings, 'SECURE_SSL_REDIRECT', False),
                'SECURE_HSTS_SECONDS': getattr(settings, 'SECURE_HSTS_SECONDS', 0) > 0,
                'SECURE_CONTENT_TYPE_NOSNIFF': getattr(settings, 'SECURE_CONTENT_TYPE_NOSNIFF', False),
                'SECURE_BROWSER_XSS_FILTER': getattr(settings, 'SECURE_BROWSER_XSS_FILTER', False),
                'X_FRAME_OPTIONS': getattr(settings, 'X_FRAME_OPTIONS', None) == 'DENY',
            }
            
            passed_checks = sum(security_checks.values())
            total_checks = len(security_checks)
            
            print(f"📊 安全检查通过: {passed_checks}/{total_checks}")
            
            for check, passed in security_checks.items():
                status = "✅" if passed else "⚠️"
                print(f"   {status} {check}")
            
            if passed_checks >= total_checks * 0.8:
                print("✅ 安全配置良好")
                self.optimization_results['security'] = True
            else:
                print("⚠️  安全配置需要改进")
                self.optimization_results['security'] = False
            
            # 安全优化建议
            optimizations = [
                "✅ 在生产环境中关闭DEBUG模式",
                "✅ 使用强密钥和环境变量",
                "✅ 启用HTTPS和安全头",
                "✅ 实施CORS策略",
                "✅ 定期更新依赖包"
            ]
            
            print("💡 安全优化建议:")
            for opt in optimizations:
                print(f"   {opt}")
                
        except Exception as e:
            print(f"❌ 安全优化检查失败: {e}")
            self.optimization_results['security'] = False
    
    def optimize_logging(self):
        """优化日志配置"""
        self.print_header("日志系统优化")
        
        try:
            from django.conf import settings
            import logging
            
            # 检查日志配置
            logging_config = getattr(settings, 'LOGGING', {})
            
            if logging_config:
                print("✅ 日志配置存在")
                
                # 检查日志目录
                logs_dir = self.project_root / 'logs'
                if logs_dir.exists():
                    print("✅ 日志目录存在")
                    
                    # 检查日志文件
                    log_files = list(logs_dir.glob('*.log'))
                    print(f"📊 日志文件数量: {len(log_files)}")
                    
                    for log_file in log_files:
                        size = log_file.stat().st_size / 1024  # KB
                        print(f"   📄 {log_file.name}: {size:.1f}KB")
                else:
                    print("⚠️  日志目录不存在")
                    logs_dir.mkdir(exist_ok=True)
                    print("✅ 已创建日志目录")
            else:
                print("⚠️  未配置日志系统")
            
            # 日志优化建议
            optimizations = [
                "✅ 配置不同级别的日志记录",
                "✅ 实施日志轮转避免文件过大",
                "✅ 使用结构化日志格式",
                "✅ 监控错误日志和性能日志",
                "✅ 在生产环境中使用集中化日志管理"
            ]
            
            print("💡 日志优化建议:")
            for opt in optimizations:
                print(f"   {opt}")
            
            self.optimization_results['logging'] = True
            
        except Exception as e:
            print(f"❌ 日志优化检查失败: {e}")
            self.optimization_results['logging'] = False
    
    def check_performance_metrics(self):
        """检查性能指标"""
        self.print_header("性能指标检查")
        
        try:
            import psutil
            
            # 系统资源使用情况
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            print(f"📊 CPU使用率: {cpu_percent}%")
            print(f"📊 内存使用率: {memory.percent}%")
            print(f"📊 磁盘使用率: {disk.percent}%")
            
            self.performance_metrics.update({
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'disk_usage': disk.percent
            })
            
            # 性能建议
            if cpu_percent > 80:
                print("⚠️  CPU使用率较高，建议优化")
            if memory.percent > 80:
                print("⚠️  内存使用率较高，建议优化")
            if disk.percent > 80:
                print("⚠️  磁盘使用率较高，建议清理")
            
            self.optimization_results['performance'] = True
            
        except ImportError:
            print("⚠️  psutil未安装，跳过性能检查")
            print("💡 安装命令: pip install psutil")
            self.optimization_results['performance'] = False
        except Exception as e:
            print(f"❌ 性能检查失败: {e}")
            self.optimization_results['performance'] = False
    
    def generate_optimization_report(self):
        """生成优化报告"""
        self.print_header("系统优化报告")
        
        total_optimizations = len(self.optimization_results)
        successful_optimizations = sum(self.optimization_results.values())
        
        print(f"📊 优化项目统计:")
        print(f"   • 总优化项: {total_optimizations}")
        print(f"   • 成功优化: {successful_optimizations}")
        print(f"   • 需要改进: {total_optimizations - successful_optimizations}")
        
        if total_optimizations > 0:
            score = (successful_optimizations / total_optimizations) * 100
            print(f"   • 优化得分: {score:.1f}%")
        else:
            score = 0
        
        print(f"\n📋 优化结果详情:")
        for optimization, result in self.optimization_results.items():
            status = "✅" if result else "❌"
            print(f"   {status} {optimization}")
        
        if self.performance_metrics:
            print(f"\n📈 性能指标:")
            for metric, value in self.performance_metrics.items():
                print(f"   • {metric}: {value}")
        
        # 生成部署建议
        print(f"\n🚀 部署准备建议:")
        deployment_checklist = [
            "✅ 配置生产环境设置文件",
            "✅ 设置环境变量和密钥",
            "✅ 配置数据库连接",
            "✅ 设置静态文件服务",
            "✅ 配置HTTPS和安全头",
            "✅ 设置监控和日志",
            "✅ 准备备份策略",
            "✅ 配置负载均衡（如需要）"
        ]
        
        for item in deployment_checklist:
            print(f"   {item}")
        
        # 保存报告
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'optimization_score': score,
            'optimization_results': self.optimization_results,
            'performance_metrics': self.performance_metrics,
            'deployment_ready': score >= 80
        }
        
        report_file = self.project_root / 'optimization_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 优化报告已保存到: {report_file}")
        
        return score >= 70
    
    def run_full_optimization(self):
        """运行完整优化"""
        print("🚀 开始SOIC系统优化...")
        
        optimizations = [
            ("数据库查询优化", self.optimize_database_queries),
            ("缓存系统优化", self.optimize_caching),
            ("静态文件优化", self.optimize_static_files),
            ("安全配置优化", self.optimize_security),
            ("日志系统优化", self.optimize_logging),
            ("性能指标检查", self.check_performance_metrics),
        ]
        
        for opt_name, opt_func in optimizations:
            try:
                opt_func()
            except Exception as e:
                print(f"❌ {opt_name}异常: {e}")
                self.optimization_results[opt_name.lower().replace(' ', '_')] = False
        
        return self.generate_optimization_report()


def main():
    """主函数"""
    optimizer = SOICSystemOptimizer()
    success = optimizer.run_full_optimization()
    
    if success:
        print("\n🎉 系统优化完成，准备就绪！")
        return 0
    else:
        print("\n💥 系统优化发现问题，请查看报告进行改进")
        return 1


if __name__ == '__main__':
    sys.exit(main())
