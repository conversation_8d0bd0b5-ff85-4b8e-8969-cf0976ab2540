#!/usr/bin/env python
"""
SOIC 开发工具集
提供代码质量检查、文档生成、测试运行等开发辅助功能
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class SOICDevTools:
    """SOIC开发工具集"""
    
    def __init__(self):
        self.project_root = project_root
        self.results = {}
    
    def print_header(self, title):
        """打印标题"""
        print(f"\n{'='*60}")
        print(f"🛠️  {title}")
        print(f"{'='*60}")
    
    def run_command(self, command, description, cwd=None):
        """运行命令并返回结果"""
        print(f"🔄 {description}...")
        
        try:
            if cwd is None:
                cwd = self.project_root
            
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print(f"✅ {description}完成")
                return True, result.stdout
            else:
                print(f"❌ {description}失败")
                print(f"错误信息: {result.stderr}")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {description}超时")
            return False, "命令执行超时"
        except Exception as e:
            print(f"❌ {description}异常: {e}")
            return False, str(e)
    
    def check_code_style(self):
        """检查代码风格"""
        self.print_header("代码风格检查")
        
        # 检查是否安装了flake8
        success, _ = self.run_command("python -m flake8 --version", "检查flake8安装")
        if not success:
            print("⚠️  flake8未安装，跳过代码风格检查")
            print("💡 安装命令: pip install flake8")
            return False
        
        # 运行flake8检查
        success, output = self.run_command(
            "python -m flake8 apps/ config/ --max-line-length=120 --exclude=migrations",
            "运行flake8代码风格检查"
        )
        
        if success and not output.strip():
            print("🎉 代码风格检查通过！")
            self.results['code_style'] = True
        else:
            print("⚠️  发现代码风格问题:")
            print(output)
            self.results['code_style'] = False
        
        return success
    
    def check_imports(self):
        """检查导入排序"""
        self.print_header("导入排序检查")
        
        # 检查是否安装了isort
        success, _ = self.run_command("python -m isort --version", "检查isort安装")
        if not success:
            print("⚠️  isort未安装，跳过导入排序检查")
            print("💡 安装命令: pip install isort")
            return False
        
        # 检查导入排序
        success, output = self.run_command(
            "python -m isort apps/ config/ --check-only --diff",
            "检查导入排序"
        )
        
        if success:
            print("✅ 导入排序正确")
            self.results['imports'] = True
        else:
            print("⚠️  导入排序需要调整:")
            print(output)
            print("💡 自动修复命令: python -m isort apps/ config/")
            self.results['imports'] = False
        
        return success
    
    def run_tests(self):
        """运行测试"""
        self.print_header("运行测试")
        
        # 检查是否安装了pytest
        success, _ = self.run_command("python -m pytest --version", "检查pytest安装")
        if not success:
            print("⚠️  pytest未安装，尝试使用Django测试")
            success, output = self.run_command(
                "python manage.py test --verbosity=2",
                "运行Django测试"
            )
        else:
            success, output = self.run_command(
                "python -m pytest -v",
                "运行pytest测试"
            )
        
        if success:
            print("✅ 测试通过")
            self.results['tests'] = True
        else:
            print("❌ 测试失败")
            print(output)
            self.results['tests'] = False
        
        return success
    
    def check_security(self):
        """安全检查"""
        self.print_header("安全检查")
        
        # Django安全检查
        success, output = self.run_command(
            "python manage.py check --deploy",
            "Django安全检查"
        )
        
        if success and "System check identified no issues" in output:
            print("✅ Django安全检查通过")
            self.results['security'] = True
        else:
            print("⚠️  发现安全问题:")
            print(output)
            self.results['security'] = False
        
        return success
    
    def generate_api_schema(self):
        """生成API Schema"""
        self.print_header("生成API Schema")
        
        success, output = self.run_command(
            "python manage.py spectacular --file api_schema.yml",
            "生成OpenAPI Schema"
        )
        
        if success:
            print("✅ API Schema生成成功")
            print(f"📄 文件位置: {self.project_root}/api_schema.yml")
            self.results['api_schema'] = True
        else:
            print("❌ API Schema生成失败")
            print(output)
            self.results['api_schema'] = False
        
        return success
    
    def check_database_migrations(self):
        """检查数据库迁移"""
        self.print_header("数据库迁移检查")
        
        # 检查是否有未应用的迁移
        success, output = self.run_command(
            "python manage.py showmigrations --plan",
            "检查迁移状态"
        )
        
        if success:
            if "[ ]" in output:
                print("⚠️  发现未应用的迁移:")
                print(output)
                self.results['migrations'] = False
            else:
                print("✅ 所有迁移已应用")
                self.results['migrations'] = True
        else:
            print("❌ 迁移检查失败")
            self.results['migrations'] = False
        
        return success
    
    def check_dependencies(self):
        """检查依赖"""
        self.print_header("依赖检查")
        
        # 检查requirements.txt
        requirements_file = self.project_root / 'requirements.txt'
        if requirements_file.exists():
            print("✅ requirements.txt存在")
            
            # 检查是否有安全漏洞
            success, output = self.run_command(
                "python -m pip check",
                "检查依赖兼容性"
            )
            
            if success and not output.strip():
                print("✅ 依赖兼容性检查通过")
                self.results['dependencies'] = True
            else:
                print("⚠️  依赖问题:")
                print(output)
                self.results['dependencies'] = False
        else:
            print("❌ requirements.txt不存在")
            self.results['dependencies'] = False
        
        return self.results.get('dependencies', False)
    
    def generate_coverage_report(self):
        """生成测试覆盖率报告"""
        self.print_header("测试覆盖率报告")
        
        # 检查是否安装了coverage
        success, _ = self.run_command("python -m coverage --version", "检查coverage安装")
        if not success:
            print("⚠️  coverage未安装，跳过覆盖率检查")
            print("💡 安装命令: pip install coverage")
            return False
        
        # 运行覆盖率测试
        success, _ = self.run_command(
            "python -m coverage run --source='.' manage.py test",
            "运行覆盖率测试"
        )
        
        if success:
            # 生成报告
            success, output = self.run_command(
                "python -m coverage report",
                "生成覆盖率报告"
            )
            
            if success:
                print("📊 测试覆盖率报告:")
                print(output)
                
                # 生成HTML报告
                self.run_command(
                    "python -m coverage html",
                    "生成HTML覆盖率报告"
                )
                print(f"📄 HTML报告位置: {self.project_root}/htmlcov/index.html")
                
                self.results['coverage'] = True
            else:
                self.results['coverage'] = False
        else:
            self.results['coverage'] = False
        
        return success
    
    def generate_report(self):
        """生成开发工具报告"""
        self.print_header("开发工具报告")
        
        total_checks = len(self.results)
        passed_checks = sum(self.results.values())
        
        print(f"📊 检查结果统计:")
        print(f"   • 总检查项: {total_checks}")
        print(f"   • 通过检查: {passed_checks}")
        print(f"   • 失败检查: {total_checks - passed_checks}")
        
        if total_checks > 0:
            score = (passed_checks / total_checks) * 100
            print(f"   • 总体得分: {score:.1f}%")
        else:
            score = 0
        
        print(f"\n📋 详细结果:")
        for check, result in self.results.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check}")
        
        # 生成建议
        print(f"\n💡 改进建议:")
        if not self.results.get('code_style', True):
            print("   • 修复代码风格问题")
        if not self.results.get('imports', True):
            print("   • 整理导入排序")
        if not self.results.get('tests', True):
            print("   • 修复失败的测试")
        if not self.results.get('security', True):
            print("   • 解决安全问题")
        if not self.results.get('migrations', True):
            print("   • 应用数据库迁移")
        
        # 保存报告
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'score': score,
            'results': self.results,
            'total_checks': total_checks,
            'passed_checks': passed_checks
        }
        
        report_file = self.project_root / 'dev_tools_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        return score >= 70
    
    def run_all_checks(self):
        """运行所有检查"""
        print("🚀 开始SOIC开发工具检查...")
        
        checks = [
            ("代码风格", self.check_code_style),
            ("导入排序", self.check_imports),
            ("安全检查", self.check_security),
            ("数据库迁移", self.check_database_migrations),
            ("依赖检查", self.check_dependencies),
            ("API Schema", self.generate_api_schema),
            ("运行测试", self.run_tests),
            ("测试覆盖率", self.generate_coverage_report),
        ]
        
        for check_name, check_func in checks:
            try:
                check_func()
            except Exception as e:
                print(f"❌ {check_name}检查异常: {e}")
                self.results[check_name.lower().replace(' ', '_')] = False
        
        return self.generate_report()


def main():
    """主函数"""
    dev_tools = SOICDevTools()
    success = dev_tools.run_all_checks()
    
    if success:
        print("\n🎉 开发工具检查完成，项目状态良好！")
        return 0
    else:
        print("\n💥 发现一些问题，请查看报告进行改进")
        return 1


if __name__ == '__main__':
    sys.exit(main())
