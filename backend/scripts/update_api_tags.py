#!/usr/bin/env python
"""
批量更新API视图的标签配置
确保所有ViewSet都有正确的中文标签
"""

import os
import sys
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 标签映射配置
TAG_MAPPINGS = {
    'apps/users/views.py': '用户认证',
    'apps/social/views.py': '社交功能',
    'apps/messaging/views.py': '消息系统',
    'apps/content/views.py': '内容管理',
    'apps/economy/views.py': '经济系统',
    'apps/core/views.py': '系统核心',
}

# ViewSet操作的中文描述模板
OPERATION_DESCRIPTIONS = {
    'list': '获取{resource}列表',
    'create': '创建{resource}',
    'retrieve': '获取{resource}详情',
    'update': '更新{resource}',
    'partial_update': '部分更新{resource}',
    'destroy': '删除{resource}',
}

# 资源名称映射
RESOURCE_MAPPINGS = {
    'User': '用户',
    'UserProfile': '用户资料',
    'UserAvatar': '用户头像',
    'UserSession': '用户会话',
    'Friendship': '好友关系',
    'Follow': '关注关系',
    'Group': '群组',
    'Recommendation': '推荐',
    'SocialStats': '社交统计',
    'SocialActivity': '社交活动',
    'Conversation': '会话',
    'Message': '消息',
    'OnlineStatus': '在线状态',
    'MessagingStats': '消息统计',
    'Post': '帖子',
    'Comment': '评论',
    'ContentCategory': '内容分类',
    'ContentStats': '内容统计',
    'Wallet': '钱包',
    'Product': '商品',
    'Order': '订单',
    'Transaction': '交易',
    'EconomyStats': '经济统计',
}


def extract_viewset_name(class_line):
    """从类定义行提取ViewSet名称"""
    match = re.search(r'class\s+(\w+)ViewSet', class_line)
    if match:
        return match.group(1)
    return None


def get_resource_name(viewset_name):
    """获取资源的中文名称"""
    return RESOURCE_MAPPINGS.get(viewset_name, viewset_name.lower())


def generate_extend_schema_view(viewset_name, tag_name):
    """生成extend_schema_view装饰器代码"""
    resource_name = get_resource_name(viewset_name)
    
    schemas = []
    for operation, template in OPERATION_DESCRIPTIONS.items():
        description = template.format(resource=resource_name)
        
        # 根据操作类型添加更详细的描述
        detailed_descriptions = {
            'list': f'获取{resource_name}列表，支持分页、搜索和筛选',
            'create': f'创建新的{resource_name}',
            'retrieve': f'根据ID获取{resource_name}的详细信息',
            'update': f'完整更新{resource_name}的所有字段',
            'partial_update': f'部分更新{resource_name}的指定字段',
            'destroy': f'删除指定的{resource_name}',
        }
        
        detailed_desc = detailed_descriptions.get(operation, description)
        
        schema = f'''    {operation}=extend_schema(
        summary="{description}",
        description="{detailed_desc}",
        tags=["{tag_name}"]
    )'''
        schemas.append(schema)
    
    decorator = f'''@extend_schema_view(
{',\n'.join(schemas)}
)'''
    return decorator


def check_file_needs_update(file_path):
    """检查文件是否需要更新"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经导入了drf_spectacular
        has_spectacular_import = 'from drf_spectacular.utils import' in content
        
        # 检查是否有ViewSet类
        has_viewsets = re.search(r'class\s+\w+ViewSet', content)
        
        # 检查是否已经有extend_schema_view装饰器
        has_decorators = '@extend_schema_view' in content
        
        return {
            'needs_import': not has_spectacular_import and has_viewsets,
            'needs_decorators': not has_decorators and has_viewsets,
            'has_viewsets': bool(has_viewsets)
        }
    except Exception as e:
        print(f"检查文件 {file_path} 时出错: {e}")
        return {'needs_import': False, 'needs_decorators': False, 'has_viewsets': False}


def add_spectacular_import(content):
    """添加drf_spectacular导入"""
    import_line = '''from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes

'''
    
    # 找到合适的位置插入导入
    lines = content.split('\n')
    insert_index = 0
    
    for i, line in enumerate(lines):
        if line.startswith('from django.') or line.startswith('from rest_framework.'):
            insert_index = i + 1
        elif line.startswith('from apps.'):
            insert_index = i
            break
    
    lines.insert(insert_index, import_line.strip())
    return '\n'.join(lines)


def update_file_tags(file_path, tag_name):
    """更新文件中的标签配置"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        check_result = check_file_needs_update(file_path)
        
        if not check_result['has_viewsets']:
            print(f"跳过 {file_path}: 没有ViewSet类")
            return False
        
        updated = False
        
        # 添加导入
        if check_result['needs_import']:
            content = add_spectacular_import(content)
            updated = True
            print(f"为 {file_path} 添加了drf_spectacular导入")
        
        # 添加装饰器
        if check_result['needs_decorators']:
            lines = content.split('\n')
            new_lines = []
            i = 0
            
            while i < len(lines):
                line = lines[i]
                
                # 检查是否是ViewSet类定义
                if re.match(r'^class\s+\w+ViewSet', line):
                    viewset_name = extract_viewset_name(line)
                    if viewset_name:
                        # 生成装饰器
                        decorator = generate_extend_schema_view(viewset_name, tag_name)
                        new_lines.append(decorator)
                        updated = True
                        print(f"为 {viewset_name}ViewSet 添加了标签装饰器")
                
                new_lines.append(line)
                i += 1
            
            content = '\n'.join(new_lines)
        
        # 写回文件
        if updated:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 更新完成: {file_path}")
            return True
        else:
            print(f"⏭️  跳过: {file_path} (无需更新)")
            return False
            
    except Exception as e:
        print(f"❌ 更新文件 {file_path} 时出错: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始批量更新API标签配置...")
    print("=" * 60)
    
    updated_count = 0
    total_count = 0
    
    for file_path, tag_name in TAG_MAPPINGS.items():
        full_path = project_root / file_path
        
        if not full_path.exists():
            print(f"⚠️  文件不存在: {full_path}")
            continue
        
        total_count += 1
        print(f"\n📝 处理文件: {file_path}")
        print(f"🏷️  标签: {tag_name}")
        
        if update_file_tags(full_path, tag_name):
            updated_count += 1
    
    print("\n" + "=" * 60)
    print("📊 更新统计:")
    print(f"   • 总文件数: {total_count}")
    print(f"   • 更新文件数: {updated_count}")
    print(f"   • 跳过文件数: {total_count - updated_count}")
    
    if updated_count > 0:
        print("\n🎉 API标签配置更新完成！")
        print("💡 建议运行以下命令验证配置:")
        print("   python manage.py spectacular --file schema.yml")
    else:
        print("\n✨ 所有文件都已是最新配置！")


if __name__ == '__main__':
    main()
