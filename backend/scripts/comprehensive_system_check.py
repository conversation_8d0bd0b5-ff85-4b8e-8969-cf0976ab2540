#!/usr/bin/env python
"""
SOIC 社交创新社区 - 综合系统检查
验证API文档、启动系统、业务功能的完整性
"""

import os
import sys
import json
import requests
import time
from pathlib import Path
from urllib.parse import urljoin

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

try:
    import django
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)


class SOICSystemChecker:
    """SOIC系统综合检查器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 10
        self.results = {
            'api_docs': {},
            'endpoints': {},
            'business_domains': {},
            'startup_system': {},
            'overall_score': 0
        }
    
    def print_header(self, title):
        """打印标题"""
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
    
    def check_server_status(self):
        """检查服务器状态"""
        self.print_header("服务器状态检查")
        
        try:
            response = self.session.get(self.base_url, timeout=5)
            if response.status_code == 200:
                print("✅ 服务器运行正常")
                print(f"📍 服务器地址: {self.base_url}")
                return True
            else:
                print(f"⚠️  服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到服务器: {e}")
            print("💡 请确保服务器已启动: python manage.py runserver")
            return False
    
    def check_api_documentation(self):
        """检查API文档"""
        self.print_header("API文档检查")
        
        # 检查主页
        try:
            response = self.session.get(self.base_url)
            if response.status_code == 200:
                content = response.text
                if "SOIC" in content and "社交创新社区" in content:
                    print("✅ 主页中文化正确")
                    self.results['api_docs']['homepage'] = True
                else:
                    print("⚠️  主页中文化可能有问题")
                    self.results['api_docs']['homepage'] = False
            else:
                print(f"❌ 主页访问失败: {response.status_code}")
                self.results['api_docs']['homepage'] = False
        except Exception as e:
            print(f"❌ 主页检查失败: {e}")
            self.results['api_docs']['homepage'] = False
        
        # 检查Swagger UI
        swagger_url = urljoin(self.base_url, "/api/docs/")
        try:
            response = self.session.get(swagger_url)
            if response.status_code == 200:
                print("✅ Swagger UI 可访问")
                self.results['api_docs']['swagger'] = True
            else:
                print(f"❌ Swagger UI 访问失败: {response.status_code}")
                self.results['api_docs']['swagger'] = False
        except Exception as e:
            print(f"❌ Swagger UI 检查失败: {e}")
            self.results['api_docs']['swagger'] = False
        
        # 检查ReDoc
        redoc_url = urljoin(self.base_url, "/api/redoc/")
        try:
            response = self.session.get(redoc_url)
            if response.status_code == 200:
                print("✅ ReDoc 可访问")
                self.results['api_docs']['redoc'] = True
            else:
                print(f"❌ ReDoc 访问失败: {response.status_code}")
                self.results['api_docs']['redoc'] = False
        except Exception as e:
            print(f"❌ ReDoc 检查失败: {e}")
            self.results['api_docs']['redoc'] = False
        
        # 检查API Schema
        schema_url = urljoin(self.base_url, "/api/schema/")
        try:
            response = self.session.get(schema_url)
            if response.status_code == 200:
                schema_data = response.json()
                
                # 检查中文标签
                tags = schema_data.get('tags', [])
                expected_tags = ['用户认证', '社交功能', '消息系统', '内容管理', '经济系统', '系统核心']
                found_tags = [tag.get('name') for tag in tags]
                
                missing_tags = [tag for tag in expected_tags if tag not in found_tags]
                if not missing_tags:
                    print("✅ API Schema 中文标签完整")
                    self.results['api_docs']['schema_tags'] = True
                else:
                    print(f"⚠️  缺少标签: {', '.join(missing_tags)}")
                    self.results['api_docs']['schema_tags'] = False
                
                # 检查API路径
                paths = schema_data.get('paths', {})
                expected_paths = ['/api/v1/auth/', '/api/v1/social/', '/api/v1/messaging/', 
                                '/api/v1/content/', '/api/v1/economy/', '/api/v1/core/']
                
                found_paths = []
                for path in paths.keys():
                    for expected in expected_paths:
                        if path.startswith(expected):
                            found_paths.append(expected)
                            break
                
                found_paths = list(set(found_paths))
                missing_paths = [path for path in expected_paths if path not in found_paths]
                
                if not missing_paths:
                    print("✅ API路径配置完整")
                    self.results['api_docs']['schema_paths'] = True
                else:
                    print(f"⚠️  缺少路径: {', '.join(missing_paths)}")
                    self.results['api_docs']['schema_paths'] = False
                
            else:
                print(f"❌ API Schema 访问失败: {response.status_code}")
                self.results['api_docs']['schema_tags'] = False
                self.results['api_docs']['schema_paths'] = False
        except Exception as e:
            print(f"❌ API Schema 检查失败: {e}")
            self.results['api_docs']['schema_tags'] = False
            self.results['api_docs']['schema_paths'] = False
    
    def check_business_endpoints(self):
        """检查业务端点"""
        self.print_header("业务端点检查")
        
        endpoints = {
            '用户认证': '/api/v1/auth/',
            '社交功能': '/api/v1/social/',
            '消息系统': '/api/v1/messaging/',
            '内容管理': '/api/v1/content/',
            '经济系统': '/api/v1/economy/',
            '系统核心': '/api/v1/core/'
        }
        
        for domain, path in endpoints.items():
            try:
                url = urljoin(self.base_url, path)
                response = self.session.get(url)
                
                if response.status_code in [200, 401, 403]:  # 200正常，401/403需要认证
                    print(f"✅ {domain} ({path}) - 端点可访问")
                    self.results['endpoints'][domain] = True
                else:
                    print(f"⚠️  {domain} ({path}) - 响应码: {response.status_code}")
                    self.results['endpoints'][domain] = False
                    
            except Exception as e:
                print(f"❌ {domain} ({path}) - 检查失败: {e}")
                self.results['endpoints'][domain] = False
    
    def check_health_endpoint(self):
        """检查健康检查端点"""
        self.print_header("健康检查端点")
        
        health_url = urljoin(self.base_url, "/api/v1/core/health/")
        try:
            response = self.session.get(health_url)
            if response.status_code == 200:
                health_data = response.json()
                status = health_data.get('status', 'unknown')
                checks = health_data.get('checks', {})
                
                print(f"✅ 健康检查端点正常 - 状态: {status}")
                print(f"📊 检查项目: {len(checks)}个")
                
                for check_name, check_result in checks.items():
                    status_icon = "✅" if check_result == 'ok' else "⚠️"
                    print(f"   {status_icon} {check_name}: {check_result}")
                
                self.results['business_domains']['health_check'] = True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                self.results['business_domains']['health_check'] = False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            self.results['business_domains']['health_check'] = False
    
    def check_admin_interface(self):
        """检查管理后台"""
        self.print_header("管理后台检查")
        
        admin_url = urljoin(self.base_url, "/admin/")
        try:
            response = self.session.get(admin_url)
            if response.status_code == 200:
                content = response.text
                if "SOIC 管理后台" in content:
                    print("✅ 管理后台中文化正确")
                    self.results['business_domains']['admin'] = True
                else:
                    print("⚠️  管理后台中文化可能有问题")
                    self.results['business_domains']['admin'] = False
            else:
                print(f"❌ 管理后台访问失败: {response.status_code}")
                self.results['business_domains']['admin'] = False
        except Exception as e:
            print(f"❌ 管理后台检查失败: {e}")
            self.results['business_domains']['admin'] = False
    
    def generate_report(self):
        """生成检查报告"""
        self.print_header("综合检查报告")
        
        # 计算各项得分
        api_docs_score = sum(self.results['api_docs'].values()) / max(len(self.results['api_docs']), 1) * 100
        endpoints_score = sum(self.results['endpoints'].values()) / max(len(self.results['endpoints']), 1) * 100
        business_score = sum(self.results['business_domains'].values()) / max(len(self.results['business_domains']), 1) * 100
        
        overall_score = (api_docs_score + endpoints_score + business_score) / 3
        
        print(f"📊 API文档得分: {api_docs_score:.1f}%")
        print(f"📊 业务端点得分: {endpoints_score:.1f}%")
        print(f"📊 业务功能得分: {business_score:.1f}%")
        print(f"🎯 综合得分: {overall_score:.1f}%")
        
        # 生成建议
        print(f"\n💡 改进建议:")
        
        if api_docs_score < 100:
            print("   • 完善API文档配置和中文化")
        
        if endpoints_score < 100:
            print("   • 检查业务端点的可访问性")
        
        if business_score < 100:
            print("   • 完善业务功能实现")
        
        if overall_score >= 90:
            print("\n🎉 系统状态优秀！")
        elif overall_score >= 70:
            print("\n👍 系统状态良好，有少量改进空间")
        else:
            print("\n⚠️  系统需要重要改进")
        
        # 保存报告
        report_data = {
            'timestamp': time.time(),
            'overall_score': overall_score,
            'detailed_scores': {
                'api_docs': api_docs_score,
                'endpoints': endpoints_score,
                'business': business_score
            },
            'results': self.results
        }
        
        report_file = project_root / 'system_check_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        return overall_score
    
    def run_comprehensive_check(self):
        """运行综合检查"""
        print("🚀 开始SOIC系统综合检查...")
        
        if not self.check_server_status():
            print("\n💥 服务器未运行，无法进行完整检查")
            return False
        
        self.check_api_documentation()
        self.check_business_endpoints()
        self.check_health_endpoint()
        self.check_admin_interface()
        
        score = self.generate_report()
        return score >= 70


def main():
    """主函数"""
    checker = SOICSystemChecker()
    success = checker.run_comprehensive_check()
    
    if success:
        print("\n🎉 系统检查完成，状态良好！")
        return 0
    else:
        print("\n💥 系统检查发现问题，请查看报告进行改进")
        return 1


if __name__ == '__main__':
    sys.exit(main())
