#!/usr/bin/env python
"""
验证API文档配置
检查DRF Spectacular配置和标签是否正确
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)


def check_spectacular_settings():
    """检查DRF Spectacular配置"""
    print("\n🔍 检查DRF Spectacular配置...")
    
    from django.conf import settings
    
    if not hasattr(settings, 'SPECTACULAR_SETTINGS'):
        print("❌ 未找到SPECTACULAR_SETTINGS配置")
        return False
    
    spectacular_settings = settings.SPECTACULAR_SETTINGS
    
    # 检查必需的配置项
    required_settings = [
        'TITLE',
        'DESCRIPTION', 
        'VERSION',
        'TAGS',
        'SERVERS',
        'SWAGGER_UI_SETTINGS',
        'REDOC_UI_SETTINGS'
    ]
    
    missing_settings = []
    for setting in required_settings:
        if setting not in spectacular_settings:
            missing_settings.append(setting)
    
    if missing_settings:
        print(f"❌ 缺少配置项: {', '.join(missing_settings)}")
        return False
    
    print("✅ SPECTACULAR_SETTINGS配置完整")
    
    # 检查标签配置
    tags = spectacular_settings.get('TAGS', [])
    expected_tags = ['用户认证', '社交功能', '消息系统', '内容管理', '经济系统', '系统核心']
    
    found_tags = [tag.get('name') for tag in tags if isinstance(tag, dict)]
    missing_tags = [tag for tag in expected_tags if tag not in found_tags]
    
    if missing_tags:
        print(f"⚠️  缺少标签: {', '.join(missing_tags)}")
    else:
        print("✅ 所有业务域标签配置完整")
    
    # 检查中文配置
    title = spectacular_settings.get('TITLE', '')
    if '社交创新社区' in title:
        print("✅ API标题已中文化")
    else:
        print("⚠️  API标题可能需要中文化")
    
    return True


def check_api_schema():
    """检查API Schema生成"""
    print("\n🔍 检查API Schema生成...")
    
    try:
        from drf_spectacular.openapi import AutoSchema
        from django.urls import get_resolver
        
        # 获取URL解析器
        resolver = get_resolver()
        
        # 检查是否有API路径
        api_patterns = []
        for pattern in resolver.url_patterns:
            if hasattr(pattern, 'pattern') and 'api/v1/' in str(pattern.pattern):
                api_patterns.append(str(pattern.pattern))
        
        if api_patterns:
            print(f"✅ 找到 {len(api_patterns)} 个API路径模式")
        else:
            print("⚠️  未找到API路径模式")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema检查失败: {e}")
        return False


def check_viewset_tags():
    """检查ViewSet标签配置"""
    print("\n🔍 检查ViewSet标签配置...")
    
    try:
        from django.apps import apps
        
        # 检查各个应用的ViewSet
        app_labels = ['users', 'social', 'messaging', 'content', 'economy', 'core']
        
        for app_label in app_labels:
            try:
                app_config = apps.get_app_config(app_label)
                views_module_path = f"{app_config.name}.views"
                
                # 动态导入views模块
                views_module = __import__(views_module_path, fromlist=[''])
                
                # 查找ViewSet类
                viewsets = []
                for attr_name in dir(views_module):
                    attr = getattr(views_module, attr_name)
                    if (isinstance(attr, type) and 
                        attr_name.endswith('ViewSet') and 
                        hasattr(attr, '__module__') and 
                        attr.__module__ == views_module_path):
                        viewsets.append(attr_name)
                
                if viewsets:
                    print(f"✅ {app_label}: 找到 {len(viewsets)} 个ViewSet")
                    for vs in viewsets:
                        print(f"   • {vs}")
                else:
                    print(f"⚠️  {app_label}: 未找到ViewSet")
                    
            except Exception as e:
                print(f"❌ {app_label}: 检查失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ViewSet标签检查失败: {e}")
        return False


def check_url_patterns():
    """检查URL模式配置"""
    print("\n🔍 检查URL模式配置...")
    
    try:
        from django.urls import get_resolver
        from django.conf import settings
        
        resolver = get_resolver()
        
        # 检查主要的API路径
        expected_paths = [
            'api/v1/auth/',
            'api/v1/social/',
            'api/v1/messaging/',
            'api/v1/content/',
            'api/v1/economy/',
            'api/v1/core/',
            'api/docs/',
            'api/redoc/',
            'admin/',
        ]
        
        found_paths = []
        for pattern in resolver.url_patterns:
            pattern_str = str(pattern.pattern)
            for expected in expected_paths:
                if expected in pattern_str:
                    found_paths.append(expected)
                    break
        
        missing_paths = [path for path in expected_paths if path not in found_paths]
        
        if missing_paths:
            print(f"⚠️  缺少URL路径: {', '.join(missing_paths)}")
        else:
            print("✅ 所有主要URL路径配置完整")
        
        print(f"✅ 找到 {len(found_paths)} 个主要路径")
        
        return True
        
    except Exception as e:
        print(f"❌ URL模式检查失败: {e}")
        return False


def generate_test_schema():
    """生成测试Schema文件"""
    print("\n🔍 生成测试Schema...")
    
    try:
        from drf_spectacular.management.commands.spectacular import Command
        from io import StringIO
        import sys
        
        # 捕获输出
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()
        
        try:
            command = Command()
            command.handle(file=None, format='yaml', validate=True)
            schema_content = captured_output.getvalue()
        finally:
            sys.stdout = old_stdout
        
        if schema_content and 'openapi:' in schema_content:
            print("✅ Schema生成成功")
            
            # 检查中文标签
            chinese_tags = ['用户认证', '社交功能', '消息系统', '内容管理', '经济系统', '系统核心']
            found_chinese = [tag for tag in chinese_tags if tag in schema_content]
            
            if found_chinese:
                print(f"✅ 找到中文标签: {', '.join(found_chinese)}")
            else:
                print("⚠️  未在Schema中找到中文标签")
            
            # 保存测试Schema文件
            schema_file = project_root / 'test_schema.yml'
            with open(schema_file, 'w', encoding='utf-8') as f:
                f.write(schema_content)
            print(f"📄 测试Schema已保存到: {schema_file}")
            
            return True
        else:
            print("❌ Schema生成失败或内容为空")
            return False
            
    except Exception as e:
        print(f"❌ Schema生成失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始验证API文档配置...")
    print("=" * 60)
    
    checks = [
        ("DRF Spectacular配置", check_spectacular_settings),
        ("API Schema生成", check_api_schema),
        ("ViewSet标签配置", check_viewset_tags),
        ("URL模式配置", check_url_patterns),
        ("测试Schema生成", generate_test_schema),
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}:")
        try:
            if check_func():
                passed_checks += 1
        except Exception as e:
            print(f"❌ {check_name}检查时出错: {e}")
    
    print("\n" + "=" * 60)
    print("📊 验证结果:")
    print(f"   • 通过检查: {passed_checks}/{total_checks}")
    print(f"   • 失败检查: {total_checks - passed_checks}/{total_checks}")
    
    if passed_checks == total_checks:
        print("\n🎉 所有检查通过！API文档配置正确。")
        print("\n💡 可以访问以下地址查看API文档:")
        print("   • Swagger UI: http://localhost:8000/api/docs/")
        print("   • ReDoc: http://localhost:8000/api/redoc/")
        return 0
    else:
        print(f"\n💥 发现 {total_checks - passed_checks} 个问题，请修复后重试。")
        return 1


if __name__ == '__main__':
    sys.exit(main())
